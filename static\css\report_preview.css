/* 8D报告预览编辑页面样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 页面头部 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 12px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header h1 {
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 15px;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 20px;
    min-height: 600px;
}

/* 左侧预览面板 */
.preview-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.preview-header {
    background: #f8f9fa;
    padding: 20px 30px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-header h2 {
    font-size: 1.4rem;
    color: #667eea;
    margin: 0;
}

.preview-actions {
    display: flex;
    gap: 10px;
}

.preview-content {
    flex: 1;
    padding: 20px 30px;
    overflow-y: auto;
    max-height: calc(100vh - 300px);
}

.preview-footer {
    padding: 20px 30px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
}

/* 右侧对话面板 */
.chat-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    background: #667eea;
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h2 {
    font-size: 1.3rem;
    margin: 0;
}

.chat-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.status-indicator {
    font-size: 0.8rem;
    color: #4ade80;
}

.chat-history {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    max-height: 400px;
    background: #fafafa;
}

.message {
    margin-bottom: 20px;
}

.ai-message .message-content {
    background: #667eea;
    color: white;
    padding: 15px 20px;
    border-radius: 18px 18px 18px 4px;
    max-width: 90%;
}

.user-message .message-content {
    background: #e5e7eb;
    color: #374151;
    padding: 15px 20px;
    border-radius: 18px 18px 4px 18px;
    max-width: 90%;
    margin-left: auto;
}

.message-content p {
    margin-bottom: 10px;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul {
    margin: 10px 0;
    padding-left: 20px;
}

.message-content li {
    margin-bottom: 5px;
}

.welcome-message {
    animation: fadeInUp 0.6s ease-out;
}

/* 快速建议 */
.quick-suggestions {
    padding: 15px 20px;
    border-top: 1px solid #e5e7eb;
    background: white;
}

.suggestions-title {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 10px;
    font-weight: 500;
}

.suggestion-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.suggestion-btn {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #4b5563;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.suggestion-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: translateY(-1px);
}

/* 输入区域 */
.chat-input-area {
    border-top: 1px solid #e5e7eb;
    background: white;
}

.input-container {
    padding: 20px;
    display: flex;
    gap: 15px;
    align-items: flex-end;
}

#chat-input {
    flex: 1;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 0.95rem;
    resize: vertical;
    min-height: 60px;
    max-height: 120px;
    transition: border-color 0.2s ease;
    font-family: inherit;
}

#chat-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-footer {
    padding: 0 20px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #6b7280;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-outline:hover {
    background: white;
    color: #667eea;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-large {
    padding: 15px 30px;
    font-size: 1.1rem;
    width: 100%;
    justify-content: center;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* D步骤内容样式 */
.d-section {
    margin-bottom: 30px;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    transition: all 0.3s ease;
}

.d-section.updated {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    animation: highlight 1s ease-out;
}

.d-section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.d-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.d-section-toggle {
    font-size: 1.2rem;
    transition: transform 0.2s ease;
}

.d-section.collapsed .d-section-toggle {
    transform: rotate(-90deg);
}

.d-section-content {
    padding: 20px;
    background: #fafafa;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    transition: max-height 0.3s ease;
    overflow: hidden;
}

.d-section.collapsed .d-section-content {
    display: none;
}

.content-item {
    margin-bottom: 15px;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.content-item:last-child {
    margin-bottom: 0;
}

.content-label {
    font-weight: 600;
    color: #667eea;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.content-value {
    color: #374151;
    line-height: 1.5;
    white-space: pre-wrap;
}

.content-value:empty:before {
    content: "暂无内容";
    color: #9ca3af;
    font-style: italic;
}

/* 加载和状态指示器 */
.loading-indicator {
    text-align: center;
    padding: 40px;
    color: #6b7280;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-left: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

.spinner-large {
    width: 60px;
    height: 60px;
    border: 6px solid #e5e7eb;
    border-left: 6px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.generation-status {
    margin-top: 15px;
}

.status-text {
    text-align: center;
    color: #667eea;
    font-weight: 500;
    margin-bottom: 10px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    animation: progress 3s ease-in-out infinite;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    min-width: 300px;
}

.loading-text {
    font-size: 1.1rem;
    color: #374151;
    font-weight: 500;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 25px 30px 20px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    margin: 0;
    color: #374151;
    font-size: 1.3rem;
}

.modal-body {
    padding: 25px 30px;
}

.modal-footer {
    padding: 20px 30px 25px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.download-links {
    margin-top: 20px;
}

.download-link {
    display: block;
    padding: 12px 20px;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    text-decoration: none;
    color: #374151;
    margin-bottom: 10px;
    transition: all 0.2s ease;
}

.download-link:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: translateY(-1px);
}

.download-link:last-child {
    margin-bottom: 0;
}

/* 动画 */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes progress {
    0% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(0%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes highlight {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0.1);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr 350px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .main-content {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header h1 {
        font-size: 1.6rem;
    }
    
    .preview-content,
    .chat-history {
        max-height: 300px;
    }
    
    .d-section-header {
        padding: 12px 15px;
    }
    
    .d-section-content {
        padding: 15px;
    }
    
    .chat-header {
        padding: 15px 20px;
    }
    
    .input-container {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.4rem;
    }
    
    .btn-large {
        padding: 12px 20px;
        font-size: 1rem;
    }
    
    .suggestion-buttons {
        flex-direction: column;
    }
    
    .suggestion-btn {
        white-space: normal;
        text-align: left;
    }
} 