4046765
nohup: ignoring input
已加载 12 个文件映射
 * Serving Flask app 'app'
 * Debug mode: off
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5555
 * Running on http://*************:5555
Press CTRL+C 扁平化后的替换数据: {'${D0标题}': '生产线A区产品尺寸超差问题汇报', '${D0汇报人}': '张三', '${D0汇报时间}': '2023年10月5日', '${D1组长姓名}': '李四', '${D1组长部门}': '生产部', '${D1组长职位}': '经理', '${D1组长主要职责}': '负责问题解决及协调', '${D1成员1姓名}': '张三', '${D1成员1部门}': '质检部', '${D1成员1职位}': '质检员', '${D1成员1主要职责}': '发现问题并报告', '${D1成员2姓名}': '王五', '${D1成员2部门}': '生产部', '${D1成员2职位}': '工程师', '${D1成员2主要职责}': '制定并实施永久措施', '${D1成员3姓名}': '赵六', '${D1成员3部门}': '质检部', '${D1成员3职位}': '主管', '${D1成员3主要职责}': '措施验证', '${D2事件整体描述}': '2023年10月5日，生产线A区质检员张三在常规检测中发现一批产品（型号X-200）尺寸超差，导致客户投诉。问题产品共50件，严重程度为高，直接影响交付计划。', '${D2何时发生}': '2023年10月5日', '${D2何地发生}': '生产线A区', '${D2何人发现}': '张三', '${D2为什么是这问题}': '测量设备校准过期导致数据偏差', '${D2发生了什么问题}': '产品尺寸超差', '${D2问题如何发生}': '测量设备校准过期导致数据偏差，生产线使用了错误的数据', '${D2问题影响程度}': '严重，直接影响交付计划', '${D3范围1}': '生产线A区', '${D3处置对策1}': '立即停用设备并重新校准，隔离不良品', '${D3责任人1}': '李四', '${D3完成期限1}': '2023年10月6日', '${D3状态1}': '已完成', '${D3进度备注1}': '经校准后设备检测合格，不良品返工后符合标准', '${D4why1}': '为什么测量设备校准过期？', '${D4answer1}': '因为设备维护人员没有按照规定时间进行校准', '${D4why2}': '为什么设备维护人员没有按照规定时间进行校准？', '${D4answer2}': '因为设备维护人员没有按照规定时间进行校准', '${D4why3}': '为什么设备维护人员没有按照规定时间进行校准？', '${D4answer3}': '因为设备维护人员没有按照规定时间进行校准', '${D4why4}': '为什么设备维护人员没有按照规定时间进行校准？', '${D4answer4}': '因为设备维护人员没有按照规定时间进行校准', '${D4why5}': '为什么设备维护人员没有按照规定时间进行校准？', '${D4answer5}': '因为设备维护人员没有按照规定时间进行校准', '${D4人原因1}': '人', '${D4人原因2}': '人', '${D4人原因3}': '人', '${D4机原因1}': '机', '${D4机原因2}': '机', '${D4机原因3}': '机', '${D4料原因1}': '料', '${D4料原因2}': '料', '${D4料原因3}': '料', '${D4法原因1}': '法', '${D4法原因2}': '法', '${D4法原因3}': '法', '${D4环原因1}': '环', '${D4环原因2}': '环', '${D4环原因3}': '环', '${D4测原因1}': '测', '${D4测原因2}': '测', '${D4测原因3}': '测', '${D4可能原因1}': '测量设备校准过期导致数据偏差', '${D4判定1}': '是', '${D4证据1}': '测量设备校准记录显示最后一次校准日期已过期', '${D4可能原因2}': '设备维护流程未定期检查校准状态', '${D4判定2}': '是', '${D4证据2}': '维护记录中未发现定期检查校准状态的记录', '${D4可能原因3}': '缺乏设备校准周期管理', '${D4判定3}': '是', '${D4证据3}': '没有设备校准周期管理表', '${D4原因小结}': '设备校准管理制度执行不到位，导致测量设备校准过期，数据偏差，最终产生质量问题', '${D5纠正措施1}': '建立设备校准周期管理表，明确责任人及校准频率，并纳入SOP', '${D5责任人1}': '王五', '${D5计划完成日期1}': '2023年11月1日', '${D6措施验证1}': '通过连续3批次产品抽样检测（每批次10件），确认尺寸合格率100%', '${D6验证人1}': '赵六', '${D6验证时间1}': '2023年11月15日', '${D6验证结果1}': '验证通过，产品尺寸符合标准', '${D7预防措施1}': '更新《设备维护管理制度》，新增校准到期预警流程，并开发自动提醒系统', '${D7责任人1}': '钱七', '${D7计划完成日期1}': '2023年12月1日', '${D8有效性确认}': '确认通过', '${D8确认人}': '赵六', '${D8确认完成时间}': '2023年11月15日'}
开始处理PPT中的所有占位符...
处理第 1 页幻灯片...
  找到占位符: ['${D0标题}', '${D0汇报时间}', '${D0汇报人}']
  发现text_frame形状
    处理段落文本: '${D0标题}...'
      替换: ${D0标题} -> 生产线A区产品尺寸超差问题汇报
      段落已更新: '生产线A区产品尺寸超差问题汇报...'
  发现text_frame形状
    处理段落文本: '汇报人：${D0汇报人}...'
      替换: ${D0汇报人} -> 张三
      段落已更新: '汇报人：张三...'
    处理段落文本: '汇报时间：${D0汇报时间}...'
      替换: ${D0汇报时间} -> 2023年10月5日
      段落已更新: '汇报时间：2023年10月5日...'
处理第 2 页幻灯片...
  找到占位符: ['${D1成员2主要职责}', '${D1成员2部门}', '${D1成员2姓名}', '${D1成员4主要职责}', '${D1成员3姓名}', '${D1成员6主要职责}', '${D1成员3主要职责}', '${D1成员5部门}', '${D1成员4部门}', '${D1成员1姓名}', '${D1组长部门}', '${D1成员3部门}', '${D1组长主要职责}', '${D1成员4职位}', '${D1组长姓名}', '${D1成员4姓名}', '${D1成员2职位}', '${D1成员5姓名}', '${D1成员1部门}', '${D1成员3职位}', '${D1成员6职位}', '${D1成员1主要职责}', '${D1成员6部门}', '${D1成员6姓名}', '${D1组长职位}', '${D1成员5主要职责}', '${D1成员1职位}', '${D1成员5职位}']
处理第 3 页幻灯片...
  找到占位符: ['${D2何时发生}', '${D2为什么是这问题}', '${D2问题如何发生}', '${D2事件整体描述}', '${D2问题影响程度}', '${D2发生了什么问题}', '${D2何人发现}', '${D2何地发生}']
  发现text_frame形状
    处理段落文本: '事件整体描述：${D2事件整体描述}...'
      替换: ${D2事件整体描述} -> 2023年10月5日，生产线A区质检员张三在常规检测中发现一批产品（型号X-200）尺寸超差，导致客户投诉。问题产品共50件，严重程度为高，直接影响交付计划。
      段落已更新: '事件整体描述：2023年10月5日，生产线A区质检员张三在常规检测中发现一批产品（型号X-200）尺...'
  发现text_frame形状
    处理段落文本: '附图说明...'
处理第 4 页幻灯片...
  找到占位符: ['${D3责任人2}', '${D3范围2}', '${D3完成期限3}', '${D3进度备注2}', '${D3责任人1}', '${D3范围4}', '${D3责任人4}', '${D3完成期限2}', '${D3状态4}', '${D3进度备注4}', '${D3责任人3}', '${D3状态3}', '${D3状态1}', '${D3范围3}', '${D3范围1}', '${D3状态2}', '${D3处置对策3}', '${D3完成期限4}', '${D3处置对策2}', '${D3完成期限1}', '${D3处置对策1}', '${D3进度备注3}', '${D3进度备注1}', '${D3处置对策4}']
处理第 5 页幻灯片...
  发现text_frame形状
    处理段落文本: '5WHY分析...'
  发现组合形状，包含 15 个子形状
  发现text_frame形状
    处理段落文本: '1WHY：${D4why1}...'
      替换: ${D4why1} -> 为什么测量设备校准过期？
      段落已更新: '1WHY：为什么测量设备校准过期？...'
  发现text_frame形状
    处理段落文本: '${D4answer1}...'
      替换: ${D4answer1} -> 因为设备维护人员没有按照规定时间进行校准
      段落已更新: '因为设备维护人员没有按照规定时间进行校准...'
  发现text_frame形状
    处理段落文本: '${D4answer2}...'
      替换: ${D4answer2} -> 因为设备维护人员没有按照规定时间进行校准
      段落已更新: '因为设备维护人员没有按照规定时间进行校准...'
  发现text_frame形状
    处理段落文本: '${D4answer3}...'
      替换: ${D4answer3} -> 因为设备维护人员没有按照规定时间进行校准
      段落已更新: '因为设备维护人员没有按照规定时间进行校准...'
  发现text_frame形状
127.0.0.1 - - [26/Jun/2025 19:14:40] "POST /process_ppt HTTP/1.1" 200 -
127.0.0.1 - - [26/Jun/2025 19:14:44] "GET / HTTP/1.1" 404 -
127.0.0.1 - - [26/Jun/2025 19:14:44] "GET /favicon.ico HTTP/1.1" 404 -
127.0.0.1 - - [26/Jun/2025 19:14:50] "GET /download/77a62414-2fa4-4c49-8241-1c5af32940bd HTTP/1.1" 200 -
127.0.0.1 - - [26/Jun/2025 19:14:55] "GET /download_file/77a62414-2fa4-4c49-8241-1c5af32940bd HTTP/1.1" 200 -
    处理段落文本: '${D4answer4}...'
      替换: ${D4answer4} -> 因为设备维护人员没有按照规定时间进行校准
      段落已更新: '因为设备维护人员没有按照规定时间进行校准...'
  发现text_frame形状
    处理段落文本: '${D4answer5}...'
      替换: ${D4answer5} -> 因为设备维护人员没有按照规定时间进行校准
      段落已更新: '因为设备维护人员没有按照规定时间进行校准...'
  发现text_frame形状
    处理段落文本: '2WHY： ${D4why2}...'
      替换: ${D4why2} -> 为什么设备维护人员没有按照规定时间进行校准？
      段落已更新: '2WHY： 为什么设备维护人员没有按照规定时间进行校准？...'
  发现text_frame形状
    处理段落文本: '3WHY： ${D4why3}...'
      替换: ${D4why3} -> 为什么设备维护人员没有按照规定时间进行校准？
      段落已更新: '3WHY： 为什么设备维护人员没有按照规定时间进行校准？...'
  发现text_frame形状
    处理段落文本: '4WHY： ${D4why4}...'
      替换: ${D4why4} -> 为什么设备维护人员没有按照规定时间进行校准？
      段落已更新: '4WHY： 为什么设备维护人员没有按照规定时间进行校准？...'
  发现text_frame形状
    处理段落文本: '5WHY： ${D4why5}...'
      替换: ${D4why5} -> 为什么设备维护人员没有按照规定时间进行校准？
      段落已更新: '5WHY： 为什么设备维护人员没有按照规定时间进行校准？...'
处理第 6 页幻灯片...
  发现text_frame形状
    处理段落文本: '鱼骨图分析...'
  发现组合形状，包含 2 个子形状
  发现text_frame形状
    处理段落文本: '${D4测原因3}...'
      替换: ${D4测原因3} -> 测
      段落已更新: '测...'
  发现组合形状，包含 54 个子形状
  发现text_frame形状
  发现text_frame形状
    处理段落文本: '环...'
  发现text_frame形状
    处理段落文本: '法...'
  发现text_frame形状
    处理段落文本: '料...'
  发现text_frame形状
    处理段落文本: '机...'
  发现text_frame形状
    处理段落文本: '人...'
  发现text_frame形状
    处理段落文本: '${D4环原因1}...'
      替换: ${D4环原因1} -> 环
      段落已更新: '环...'
  发现text_frame形状
    处理段落文本: '${D4环原因2}...'
      替换: ${D4环原因2} -> 环
      段落已更新: '环...'
  发现text_frame形状
    处理段落文本: '${D4环原因3}...'
      替换: ${D4环原因3} -> 环
      段落已更新: '环...'
  发现text_frame形状
    处理段落文本: '${D4法原因1}...'
      替换: ${D4法原因1} -> 法
      段落已更新: '法...'
  发现text_frame形状
    处理段落文本: '${D4法原因3}...'
      替换: ${D4法原因3} -> 法
      段落已更新: '法...'
  发现text_frame形状
    处理段落文本: '${D4法原因2}...'
      替换: ${D4法原因2} -> 法
      段落已更新: '法...'
  发现text_frame形状
    处理段落文本: '${D4料原因1}...'
      替换: ${D4料原因1} -> 料
      段落已更新: '料...'
  发现text_frame形状
    处理段落文本: '${D4料原因2}...'
      替换: ${D4料原因2} -> 料
      段落已更新: '料...'
  发现text_frame形状
    处理段落文本: '${D4料原因3}...'
      替换: ${D4料原因3} -> 料
      段落已更新: '料...'
  发现text_frame形状
    处理段落文本: '${D4机原因1}...'
      替换: ${D4机原因1} -> 机
      段落已更新: '机...'
  发现text_frame形状
    处理段落文本: '${D4机原因3}...'
      替换: ${D4机原因3} -> 机
      段落已更新: '机...'
  发现text_frame形状
    处理段落文本: '${D4机原因2}...'
      替换: ${D4机原因2} -> 机
      段落已更新: '机...'
  发现text_frame形状
    处理段落文本: '${D4人原因1}...'
      替换: ${D4人原因1} -> 人
      段落已更新: '人...'
  发现text_frame形状
    处理段落文本: '${D4人原因2}...'
      替换: ${D4人原因2} -> 人
      段落已更新: '人...'
  发现text_frame形状
    处理段落文本: '${D4人原因3}...'
      替换: ${D4人原因3} -> 人
      段落已更新: '人...'
  发现text_frame形状
    处理段落文本: '测...'
  发现text_frame形状
    处理段落文本: '${D4测原因1}...'
      替换: ${D4测原因1} -> 测
      段落已更新: '测...'
  发现text_frame形状
    处理段落文本: '${D4测原因2}...'
      替换: ${D4测原因2} -> 测
      段落已更新: '测...'
  发现text_frame形状
  发现text_frame形状
  发现text_frame形状
  发现text_frame形状
  发现text_frame形状
  发现text_frame形状
处理第 7 页幻灯片...
  找到占位符: ['${D4可能原因1}', '${D4可能原因7}', '${D4证据3}', '${D4证据4}', '${D4判定3}', '${D4可能原因3}', '${D4证据5}', '${D4可能原因5}', '${D4判定4}', '${D4证据1}', '${D4判定5}', '${D4证据2}', '${D4可能原因6}', '${D4判定7}', '${D4判定1}', '${D4原因小结}', '${D4判定6}', '${D4证据7}', '${D4可能原因2}', '${D4判定2}', '${D4可能原因4}', '${D4证据6}']
  发现text_frame形状
    处理段落文本: ' ...'
  发现text_frame形状
    处理段落文本: '小结：${D4原因小结}；...'
      替换: ${D4原因小结} -> 设备校准管理制度执行不到位，导致测量设备校准过期，数据偏差，最终产生质量问题
      段落已更新: '小结：设备校准管理制度执行不到位，导致测量设备校准过期，数据偏差，最终产生质量问题；...'
  发现text_frame形状
    处理段落文本: '可能原因分析...'
处理第 8 页幻灯片...
  找到占位符: ['${D5责任人2}', '${D5计划完成日期1}', '${D5纠正措施3}', '${D5纠正措施4}', '${D5纠正措施1}', '${D5责任人4}', '${D5责任人1}', '${D5责任人3}', '${D5计划完成日期4}', '${D5计划完成日期3}', '${D5计划完成日期2}', '${D5纠正措施2}']
处理第 9 页幻灯片...
  找到占位符: ['${D6验证结果4}', '${D6验证时间2}', '${D6验证时间3}', '${D6验证人3}', '${D6措施验证4}', '${D6验证时间4}', '${D6验证结果2}', '${D6措施验证2}', '${D6措施验证1}', '${D6验证人2}', '${D6措施验证3}', '${D6验证时间1}', '${D6验证人1}', '${D6验证结果3}', '${D6验证人4}', '${D6验证结果1}']
处理第 10 页幻灯片...
  找到占位符: ['${D7预防措施1}', '${D7责任人3}', '${D7计划完成日期3}', '${D7责任人4}', '${D7计划完成日期4}', '${D7预防措施2}', '${D7计划完成日期2}', '${D7预防措施3}', '${D7责任人2}', '${D7预防措施4}', '${D7计划完成日期1}', '${D7责任人1}']
处理第 11 页幻灯片...
  找到占位符: ['${D8确认完成时间}', '${D8确认人}', '${D8有效性确认}']
处理第 12 页幻灯片...
PPT处理完成

加载DOCX文档: template.docx
扁平化后的替换数据: {'${D0标题}': '产品缺陷导致客户投诉事件分析报告', '${D0汇报人}': '质量部经理张三', '${D0汇报时间}': '2023-11-15', '${D1组长姓名}': '质量部经理李四', '${D1组长部门}': '质量部', '${D1组长职位}': '经理', '${D1组长主要职责}': '', '${D1成员1姓名}': '生产部主管王五', '${D1成员1部门}': '生产部', '${D1成员1职位}': '主管', '${D1成员1主要职责}': '负责生产流程审查', '${D1成员2姓名}': '技术工程师赵六', '${D1成员2部门}': '技术部', '${D1成员2职位}': '工程师', '${D1成员2主要职责}': '负责技术原因分析', '${D1成员3姓名}': '质检员陈七', '${D1成员3部门}': '质量部', '${D1成员3职位}': '质检员', '${D1成员3主要职责}': '负责检测方法优化', '${D2事件整体描述}': '2023-11-10，某产线A批次产品；生产人员发现产品表面划痕；客户反馈产品外观缺陷导致退货率上升；问题表现为产品表面存在不可接受的划痕；划痕可能源于设备磨损或操作不当；影响约300件产品，客户投诉率上升15%', '${D2何时发生}': '2023-11-10', '${D2何地发生}': '某产线', '${D2何人发现}': '生产人员', '${D2为什么是这问题}': '划痕可能源于设备磨损或操作不当', '${D2发生了什么问题}': '产品表面存在不可接受的划痕', '${D2问题如何发生}': '设备导辊磨损和操作不当', '${D2问题影响程度}': '影响约300件产品，客户投诉率上升15%', '${D3范围1}': 'A批次产品隔离', '${D3处置对策1}': '立即停机检查设备、启动追溯流程', '${D3责任人1}': '生产主管王五', '${D3完成期限1}': '2023-11-12', '${D3状态1}': '已完成', '${D3进度备注1}': '已隔离问题产品并通知客户', '${D4why1}': '为何有划痕？', '${D4answer1}': '设备导辊磨损', '${D4why2}': '为何导辊磨损？', '${D4answer2}': '未按周期维护', '${D4why3}': '为何未维护？', '${D4answer3}': '未制定维护计划', '${D4why4}': '为何无计划？', '${D4answer4}': '设备管理制度缺失', '${D4why5}': '为何制度缺失？', '${D4answer5}': '未识别此风险', '${D4人原因1}': '操作员未检查设备', '${D4人原因2}': '', '${D4人原因3}': '', '${D4机原因1}': '导辊磨损未更换', '${D4机原因2}': '', '${D4机原因3}': '', '${D4料原因1}': '润滑剂不足', '${D4料原因2}': '', '${D4料原因3}': '', '${D4法原因1}': '无维护SOP', '${D4法原因2}': '', '${D4法原因3}': '', '${D4环原因1}': '车间湿度异常', '${D4环原因2}': '', '${D4环原因3}': '', '${D4测原因1}': '检测标准不明确', '${D4测原因2}': '', '${D4测原因3}': '', '${D4可能原因1}': '设备维护缺失', '${D4判定1}': '是', '${D4证据1}': '设备记录无维护记录', '${D4可能原因2}': '操作培训不足', '${D4判定2}': '是', '${D4证据2}': '员工未通过最新操作考核', '${D4可能原因3}': '', '${D4判定3}': '', '${D4证据3}': '', '${D4原因小结}': '主因判定：设备维护缺失、操作培训不足', '${D5纠正措施1}': '制定设备维护计划并培训操作员', '${D5责任人1}': '技术工程师赵六', '${D5计划完成日期1}': '2023-12-01', '${D6措施验证1}': '抽样检测200件产品划痕率', '${D6验证人1}': '质检员陈七', '${D6验证时间1}': '2023-12-05', '${D6验证结果1}': '划痕率降至0.5%以下，符合标准', '${D7预防措施1}': '更新设备维护SOP、增加操作员月度考核、安装设备磨损预警传感器', '${D7责任人1}': '质量部经理李四', '${D7计划完成日期1}': '2023-11-30', '${D8有效性确认}': '客户投诉率下降至正常水平', '${D8确认人}': '客户服务总监', '${D8确认完成时间}': '2023-12-10'}
开始处理DOCX中的所有占位符...
处理文档段落...
找到 41 个表格（包括嵌套表格）
处理表格 1:
表格大小: 20行 x 5列
处理单元格 [8,1]: '事件整体描述：${D2事件整体描述}'
    找到匹配: ${D2事件整体描述} -> 2023-11-10，某产线A批次产品；生产人员发现产品表面划痕；客户反馈产品外观缺陷导致退货率上升；问题表现为产品表面存在不可接受的划痕；划痕可能源于设备磨损或操作不当；影响约300件产品，客户投诉率上升15%
    替换结果: '事件整体描述：${D2事件整体描述}' -> '事件整体描述：2023-11-10，某产线A批次产品；生产人员发现产品表面划痕；客户反馈产品外观缺陷导致退货率上升；问题表现为产品表面存在不可接受的划痕；划痕可能源于设备磨损或操作不当；影响约300件产品，客户投诉率上升15%'
处理单元格 [12,1]: '5Why分析：
产生原因小结：${D4原因小结}'
    找到匹配: ${D4原因小结} -> 主因判定：设备维护缺失、操作培训不足
    替换结果: '产生原因小结：${D4原因小结} ' -> '产生原因小结：主因判定：设备维护缺失、操作培训不足 '
处理单元格 [20,1]: '确认文件内容及有效性：
${D8有效性确认}
关闭确认人/日期：${D8确认人}  ${D8确认完成时间}'
    找到匹配: ${D8有效性确认} -> 客户投诉率下降至正常水平
    替换结果: '${D8有效性确认}' -> '客户投诉率下降至正常水平'
    找到匹配: ${D8确认人} -> 客户服务总监
    找到匹配: ${D8确认完成时间} -> 2023-12-10
    替换结果: '关闭确认人/日期：${D8确认人}  ${D8确认完成时间}' -> '关闭确认人/日期：客户服务总监  2023-12-10'
表格 1 处理完成，有替换
处理表格 2:
表格大小: 8行 x 5列
处理单元格 [2,2]: '${D1组长姓名}'
    找到匹配: ${D1组长姓名} -> 质量部经理李四
    替换结果: '${D1组长姓名}' -> '质量部经理李四'
处理单元格 [2,3]: '${D1组长部门}'
    找到匹配: ${D1组长部门} -> 质量部
    替换结果: '${D1组长部门}' -> '质量部'
处理单元格 [2,4]: '${D1组长职位}'
    找到匹配: ${D1组长职位} -> 经理
    替换结果: '${D1组长职位}' -> '经理'
处理单元格 [2,5]: '${D1组长主要职责}'
    找到匹配: ${D1组长主要职责} -> 
    替换结果: '${D1组长主要职责}' -> ''
处理单元格 [3,2]: '${D1成员1姓名}'
    找到匹配: ${D1成员1姓名} -> 生产部主管王五
    替换结果: '${D1成员1姓名}' -> '生产部主管王五'
处理单元格 [3,3]: '${D1成员1部门}'
    找到匹配: ${D1成员1部门} -> 生产部
    替换结果: '${D1成员1部门}' -> '生产部'
处理单元格 [3,4]: '${D1成员1职位}'
    找到匹配: ${D1成员1职位} -> 主管
    替换结果: '${D1成员1职位}' -> '主管'
处理单元格 [3,5]: '${D1成员1主要职责}'
    找到匹配: ${D1成员1主要职责} -> 负责生产流程审查
    替换结果: '${D1成员1主要职责}' -> '负责生产流程审查'
处理单元格 [4,2]: '${D1成员2姓名}'
    找到匹配: ${D1成员2姓名} -> 技术工程师赵六
    替换结果: '${D1成员2姓名}' -> '技术工程师赵六'
处理单元格 [4,3]: '${D1成员2部门}'
    找到匹配: ${D1成员2部门} -> 技术部
    替换结果: '${D1成员2部门}' -> '技术部'
处理单元格 [4,4]: '${D1成员2职位}'
    找到匹配: ${D1成员2职位} -> 工程师
    替换结果: '${D1成员2职位}' -> '工程师'
处理单元格 [4,5]: '${D1成员2主要职责}'
    找到匹配: ${D1成员2主要职责} -> 负责技术原因分析
    替换结果: '${D1成员2主要职责}' -> '负责技术原因分析'
处理单元格 [5,2]: '${D1成员3姓名}'
    找到匹配: ${D1成员3姓名} -> 质检员陈七
    替换结果: '${D1成员3姓名}' -> '质检员陈七'
处理单元格 [5,3]: '${D1成员3部门}'
    找到匹配: ${D1成员3部门} -> 质量部
    替换结果: '${D1成员3部门}' -> '质量部'
处理单元格 [5,4]: '${D1成员3职位}'
    找到匹配: ${D1成员3职位} -> 质检员
    替换结果: '${D1成员3职位}' -> '质检员'
处理单元格 [5,5]: '${D1成员3主要职责}'
    找到匹配: ${D1成员3主要职责} -> 负责检测方法优化
    替换结果: '${D1成员3主要职责}' -> '负责检测方法优化'
处理单元格 [6,2]: '${D1成员4姓名}'
    删除未匹配的占位符: ['${D1成员4姓名}']
    替换结果: '${D1成员4姓名}' -> ''
处理单元格 [6,3]: '${D1成员4部门}'
    删除未匹配的占位符: ['${D1成员4部门}']
    替换结果: '${D1成员4部门}' -> ''
处理单元格 [6,4]: '${D1成员4职位}'
    删除未匹配的占位符: ['${D1成员4职位}']
    替换结果: '${D1成员4职位}' -> ''
处理单元格 [6,5]: '${D1成员4主要职责}'
    删除未匹配的占位符: ['${D1成员4主要职责}']
    替换结果: '${D1成员4主要职责}' -> ''
处理单元格 [7,2]: '${D1成员5姓名}'
    删除未匹配的占位符: ['${D1成员5姓名}']
    替换结果: '${D1成员5姓名}' -> ''
处理单元格 [7,3]: '${D1成员5部门}'
    删除未匹配的占位符: ['${D1成员5部门}']
    替换结果: '${D1成员5部门}' -> ''
处理单元格 [7,4]: '${D1成员5职位}'
    删除未匹配的占位符: ['${D1成员5职位}']
    替换结果: '${D1成员5职位}' -> ''
处理单元格 [7,5]: '${D1成员5主要职责}'
    删除未匹配的占位符: ['${D1成员5主要职责}']
    替换结果: '${D1成员5主要职责}' -> ''
处理单元格 [8,2]: '${D1成员6姓名}'
    删除未匹配的占位符: ['${D1成员6姓名}']
    替换结果: '${D1成员6姓名}' -> ''
处理单元格 [8,3]: '${D1成员6部门}'
    删除未匹配的占位符: ['${D1成员6部门}']
    替换结果: '${D1成员6部门}' -> ''
处理单元格 [8,4]: '${D1成员6职位}'
    删除未匹配的占位符: ['${D1成员6职位}']
    替换结果: '${D1成员6职位}' -> ''
处理单元格 [8,5]: '${D1成员6主要职责}'
    删除未匹配的占位符: ['${D1成员6主要职责}']
    替换结果: '${D1成员6主要职责}' -> ''
表格 2 处理完成，有替换
处理表格 3:
表格大小: 8行 x 5列
扁平化后的替换数据: {'${D0标题}': '产品缺陷导致客户投诉事件分析报告', '${D0汇报人}': '质量部经理张三', '${D0汇报时间}': '2023-11-15', '${D1组长姓名}': '质量部经理李四', '${D1组长部门}': '质量部', '${D1组长职位}': '经理', '${D1组长主要职责}': '', '${D1成员1姓名}': '生产部主管王五', '${D1成员1部门}': '生产部', '${D1成员1职位}': '主管', '${D1成员1主要职责}': '负责生产流程审查', '${D1成员2姓名}': '技术工程师赵六', '${D1成员2部门}': '技术部', '${D1成员2职位}': '工程师', '${D1成员2主要职责}': '负责技术原因分析', '${D1成员3姓名}': '质检员陈七', '${D1成员3部门}': '质量部', '${D1成员3职位}': '质检员', '${D1成员3主要职责}': '负责检测方法优化', '${D2事件整体描述}': '2023-11-10，某产线A批次产品；生产人员发现产品表面划痕；客户反馈产品外观缺陷导致退货率上升；问题表现为产品表面存在不可接受的划痕；划痕可能源于设备磨损或操作不当；影响约300件产品，客户投诉率上升15%', '${D2何时发生}': '2023-11-10', '${D2何地发生}': '某产线', '${D2何人发现}': '生产人员', '${D2为什么是这问题}': '划痕可能源于设备磨损或操作不当', '${D2发生了什么问题}': '产品表面存在不可接受的划痕', '${D2问题如何发生}': '设备导辊磨损和操作不当', '${D2问题影响程度}': '影响约300件产品，客户投诉率上升15%', '${D3范围1}': 'A批次产品隔离', '${D3处置对策1}': '立即停机检查设备、启动追溯流程', '${D3责任人1}': '生产主管王五', '${D3完成期限1}': '2023-11-12', '${D3状态1}': '已完成', '${D3进度备注1}': '已隔离问题产品并通知客户', '${D4why1}': '为何有划痕？', '${D4answer1}': '设备导辊磨损', '${D4why2}': '为何导辊磨损？', '${D4answer2}': '未按周期维护', '${D4why3}': '为何未维护？', '${D4answer3}': '未制定维护计划', '${D4why4}': '为何无计划？', '${D4answer4}': '设备管理制度缺失', '${D4why5}': '为何制度缺失？', '${D4answer5}': '未识别此风险', '${D4人原因1}': '操作员未检查设备', '${D4人原因2}': '', '${D4人原因3}': '', '${D4机原因1}': '导辊磨损未更换', '${D4机原因2}': '', '${D4机原因3}': '', '${D4料原因1}': '润滑剂不足', '${D4料原因2}': '', '${D4料原因3}': '', '${D4法原因1}': '无维护SOP', '${D4法原因2}': '', '${D4法原因3}': '', '${D4环原因1}': '车间湿度异常', '${D4环原因2}': '', '${D4环原因3}': '', '${D4测原因1}': '检测标准不明确', '${D4测原因2}': '', '${D4测原因3}': '', '${D4可能原因1}': '设备维护缺失', '${D4判定1}': '是', '${D4证据1}': '设备记录无维护记录', '${D4可能原因2}': '操作培训不足', '${D4判定2}': '是', '${D4证据2}': '员工未通过最新操作考核', '${D4可能原因3}': '', '${D4判定3}': '', '${D4证据3}': '', '${D4原因小结}': '主因判定：设备维护缺失、操作培训不足', '${D5纠正措施1}': '制定设备维护计划并培训操作员', '${D5责任人1}': '技术工程师赵六', '${D5计划完成日期1}': '2023-12-01', '${D6措施验证1}': '抽样检测200件产品划痕率', '${D6验证人1}': '质检员陈七', '${D6验证时间1}': '2023-12-05', '${D6验证结果1}': '划痕率降至0.5%以下，符合标准', '${D7预防措施1}': '更新设备维护SOP、增加操作员月度考核、安装设备磨损预警传感器', '${D7责任人1}': '质量部经理李四', '${D7计划完成日期1}': '2023-11-30', '${D8有效性确认}': '客户投诉率下降至正常水平', '${D8确认人}': '客户服务总监', '${D8确认完成时间}': '2023-12-10'}
开始处理PPT中的所有占位符...
处理第 1 页幻灯片...
  找到占位符: ['${D0标题}', '${D0汇报时间}', '${D0汇报人}']
  发现text_frame形状
    处理段落文本: '${D0标题}...'
      替换: ${D0标题} -> 产品缺陷导致客户投诉事件分析报告
      段落已更新: '产品缺陷导致客户投诉事件分析报告...'
  发现text_frame形状
    处理段落文本: '汇报人：${D0汇报人}...'
      替换: ${D0汇报人} -> 质量部经理张三
      段落已更新: '汇报人：质量部经理张三...'
    处理段落文本: '汇报时间：${D0汇报时间}...'
      替换: ${D0汇报时间} -> 2023-11-15
表格 3 处理完成，无替换
处理表格 4:
表格大小: 8行 x 5列
      段落已更新: '汇报时间：2023-11-15...'
处理第 2 页幻灯片...
  找到占位符: ['${D1成员2主要职责}', '${D1成员2部门}', '${D1成员2姓名}', '${D1成员4主要职责}', '${D1成员3姓名}', '${D1成员6主要职责}', '${D1成员3主要职责}', '${D1成员5部门}', '${D1成员4部门}', '${D1成员1姓名}', '${D1组长部门}', '${D1成员3部门}', '${D1组长主要职责}', '${D1成员4职位}', '${D1组长姓名}', '${D1成员4姓名}', '${D1成员2职位}', '${D1成员5姓名}', '${D1成员1部门}', '${D1成员3职位}', '${D1成员6职位}', '${D1成员1主要职责}', '${D1成员6部门}', '${D1成员6姓名}', '${D1组长职位}', '${D1成员5主要职责}', '${D1成员1职位}', '${D1成员5职位}']
处理第 3 页幻灯片...
  找到占位符: ['${D2何时发生}', '${D2为什么是这问题}', '${D2问题如何发生}', '${D2事件整体描述}', '${D2问题影响程度}', '${D2发生了什么问题}', '${D2何人发现}', '${D2何地发生}']
  发现text_frame形状
    处理段落文本: '事件整体描述：${D2事件整体描述}...'
      替换: ${D2事件整体描述} -> 2023-11-10，某产线A批次产品；生产人员发现产品表面划痕；客户反馈产品外观缺陷导致退货率上升；问题表现为产品表面存在不可接受的划痕；划痕可能源于设备磨损或操作不当；影响约300件产品，客户投诉率上升15%
      段落已更新: '事件整体描述：2023-11-10，某产线A批次产品；生产人员发现产品表面划痕；客户反馈产品外观缺陷...'
  发现text_frame形状
    处理段落文本: '附图说明...'
处理第 4 页幻灯片...
  找到占位符: ['${D3责任人2}', '${D3范围2}', '${D3完成期限3}', '${D3进度备注2}', '${D3责任人1}', '${D3范围4}', '${D3责任人4}', '${D3完成期限2}', '${D3状态4}', '${D3进度备注4}', '${D3责任人3}', '${D3状态3}', '${D3状态1}', '${D3范围3}', '${D3范围1}', '${D3状态2}', '${D3处置对策3}', '${D3完成期限4}', '${D3处置对策2}', '${D3完成期限1}', '${D3处置对策1}', '${D3进度备注3}', '${D3进度备注1}', '${D3处置对策4}']
表格 4 处理完成，无替换
处理表格 5:
表格大小: 8行 x 5列
处理第 5 页幻灯片...
  发现text_frame形状
    处理段落文本: '5WHY分析...'
  发现组合形状，包含 15 个子形状
  发现text_frame形状
    处理段落文本: '1WHY：${D4why1}...'
      替换: ${D4why1} -> 为何有划痕？
      段落已更新: '1WHY：为何有划痕？...'
  发现text_frame形状
    处理段落文本: '${D4answer1}...'
      替换: ${D4answer1} -> 设备导辊磨损
      段落已更新: '设备导辊磨损...'
  发现text_frame形状
    处理段落文本: '${D4answer2}...'
      替换: ${D4answer2} -> 未按周期维护
      段落已更新: '未按周期维护...'
  发现text_frame形状
    处理段落文本: '${D4answer3}...'
      替换: ${D4answer3} -> 未制定维护计划
      段落已更新: '未制定维护计划...'
  发现text_frame形状
    处理段落文本: '${D4answer4}...'
      替换: ${D4answer4} -> 设备管理制度缺失
      段落已更新: '设备管理制度缺失...'
  发现text_frame形状
    处理段落文本: '${D4answer5}...'
      替换: ${D4answer5} -> 未识别此风险
      段落已更新: '未识别此风险...'
  发现text_frame形状
    处理段落文本: '2WHY： ${D4why2}...'
      替换: ${D4why2} -> 为何导辊磨损？
      段落已更新: '2WHY： 为何导辊磨损？...'
  发现text_frame形状
    处理段落文本: '3WHY： ${D4why3}...'
      替换: ${D4why3} -> 为何未维护？
      段落已更新: '3WHY： 为何未维护？...'
  发现text_frame形状
    处理段落文本: '4WHY： ${D4why4}...'
      替换: ${D4why4} -> 为何无计划？
      段落已更新: '4WHY： 为何无计划？...'
  发现text_frame形状
    处理段落文本: '5WHY： ${D4why5}...'
      替换: ${D4why5} -> 为何制度缺失？
      段落已更新: '5WHY： 为何制度缺失？...'
处理第 6 页幻灯片...
  发现text_frame形状
    处理段落文本: '鱼骨图分析...'
  发现组合形状，包含 2 个子形状
  发现text_frame形状
    处理段落文本: '${D4测原因3}...'
      替换: ${D4测原因3} -> 
      段落已更新: '...'
  发现组合形状，包含 54 个子形状
  发现text_frame形状
  发现text_frame形状
    处理段落文本: '环...'
  发现text_frame形状
    处理段落文本: '法...'
  发现text_frame形状
    处理段落文本: '料...'
  发现text_frame形状
    处理段落文本: '机...'
  发现text_frame形状
    处理段落文本: '人...'
  发现text_frame形状
    处理段落文本: '${D4环原因1}...'
      替换: ${D4环原因1} -> 车间湿度异常
      段落已更新: '车间湿度异常...'
  发现text_frame形状
    处理段落文本: '${D4环原因2}...'
      替换: ${D4环原因2} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4环原因3}...'
      替换: ${D4环原因3} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4法原因1}...'
      替换: ${D4法原因1} -> 无维护SOP
      段落已更新: '无维护SOP...'
  发现text_frame形状
    处理段落文本: '${D4法原因3}...'
      替换: ${D4法原因3} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4法原因2}...'
      替换: ${D4法原因2} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4料原因1}...'
      替换: ${D4料原因1} -> 润滑剂不足
      段落已更新: '润滑剂不足...'
  发现text_frame形状
    处理段落文本: '${D4料原因2}...'
      替换: ${D4料原因2} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4料原因3}...'
      替换: ${D4料原因3} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4机原因1}...'
      替换: ${D4机原因1} -> 导辊磨损未更换
      段落已更新: '导辊磨损未更换...'
  发现text_frame形状
    处理段落文本: '${D4机原因3}...'
      替换: ${D4机原因3} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4机原因2}...'
      替换: ${D4机原因2} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4人原因1}...'
      替换: ${D4人原因1} -> 操作员未检查设备
      段落已更新: '操作员未检查设备...'
  发现text_frame形状
    处理段落文本: '${D4人原因2}...'
      替换: ${D4人原因2} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4人原因3}...'
      替换: ${D4人原因3} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '测...'
  发现text_frame形状
    处理段落文本: '${D4测原因1}...'
      替换: ${D4测原因1} -> 检测标准不明确
      段落已更新: '检测标准不明确...'
  发现text_frame形状
    处理段落文本: '${D4测原因2}...'
      替换: ${D4测原因2} -> 
      段落已更新: '...'
  发现text_frame形状
  发现text_frame形状
  发现text_frame形状
  发现text_frame形状
  发现text_frame形状
  发现text_frame形状
处理第 7 页幻灯片...
  找到占位符: ['${D4可能原因1}', '${D4可能原因7}', '${D4证据3}', '${D4证据4}', '${D4判定3}', '${D4可能原因3}', '${D4证据5}', '${D4可能原因5}', '${D4判定4}', '${D4证据1}', '${D4判定5}', '${D4证据2}', '${D4可能原因6}', '${D4判定7}', '${D4判定1}', '${D4原因小结}', '${D4判定6}', '${D4证据7}', '${D4可能原因2}', '${D4判定2}', '${D4可能原因4}', '${D4证据6}']
  发现text_frame形状
    处理段落文本: ' ...'
表格 5 处理完成，无替换
处理表格 6:
表格大小: 8行 x 5列
  发现text_frame形状
    处理段落文本: '小结：${D4原因小结}；...'
      替换: ${D4原因小结} -> 主因判定：设备维护缺失、操作培训不足
      段落已更新: '小结：主因判定：设备维护缺失、操作培训不足；...'
  发现text_frame形状
    处理段落文本: '可能原因分析...'
处理第 8 页幻灯片...
  找到占位符: ['${D5责任人2}', '${D5计划完成日期1}', '${D5纠正措施3}', '${D5纠正措施4}', '${D5纠正措施1}', '${D5责任人4}', '${D5责任人1}', '${D5责任人3}', '${D5计划完成日期4}', '${D5计划完成日期3}', '${D5计划完成日期2}', '${D5纠正措施2}']
处理第 9 页幻灯片...
  找到占位符: ['${D6验证结果4}', '${D6验证时间2}', '${D6验证时间3}', '${D6验证人3}', '${D6措施验证4}', '${D6验证时间4}', '${D6验证结果2}', '${D6措施验证2}', '${D6措施验证1}', '${D6验证人2}', '${D6措施验证3}', '${D6验证时间1}', '${D6验证人1}', '${D6验证结果3}', '${D6验证人4}', '${D6验证结果1}']
处理第 10 页幻灯片...
  找到占位符: ['${D7预防措施1}', '${D7责任人3}', '${D7计划完成日期3}', '${D7责任人4}', '${D7计划完成日期4}', '${D7预防措施2}', '${D7计划完成日期2}', '${D7预防措施3}', '${D7责任人2}', '${D7预防措施4}', '${D7计划完成日期1}', '${D7责任人1}']
表格 6 处理完成，无替换
处理表格 7:
表格大小: 8行 x 2列
处理单元格 [2,2]: '${D2何时发生}'
处理第 11 页幻灯片...
    找到匹配: ${D2何时发生} -> 2023-11-10
    替换结果: '${D2何时发生}' -> '2023-11-10'
  找到占位符: ['${D8确认完成时间}', '${D8确认人}', '${D8有效性确认}']
处理第 12 页幻灯片...
PPT处理完成
处理单元格 [3,2]: '${D2何地发生}'
    找到匹配: ${D2何地发生} -> 某产线
    替换结果: '${D2何地发生}' -> '某产线'
处理单元格 [4,2]: '${D2何人发现}'
    找到匹配: ${D2何人发现} -> 生产人员
    替换结果: '${D2何人发现}' -> '生产人员'
处理单元格 [5,2]: '${D2为什么是这问题}'
    找到匹配: ${D2为什么是这问题} -> 划痕可能源于设备磨损或操作不当
    替换结果: '${D2为什么是这问题}' -> '划痕可能源于设备磨损或操作不当'
处理单元格 [6,2]: '${D2发生了什么问题}'
    找到匹配: ${D2发生了什么问题} -> 产品表面存在不可接受的划痕
    替换结果: '${D2发生了什么问题}' -> '产品表面存在不可接受的划痕'
处理单元格 [7,2]: '${D2问题如何发生}'
    找到匹配: ${D2问题如何发生} -> 设备导辊磨损和操作不当
    替换结果: '${D2问题如何发生}' -> '设备导辊磨损和操作不当'
处理单元格 [8,2]: '${D2问题影响程度}'
    找到匹配: ${D2问题影响程度} -> 影响约300件产品，客户投诉率上升15%
    替换结果: '${D2问题影响程度}' -> '影响约300件产品，客户投诉率上升15%'
表格 7 处理完成，有替换
处理表格 8:
表格大小: 8行 x 2列
表格 8 处理完成，无替换
处理表格 9:
表格大小: 8行 x 2列
表格 9 处理完成，无替换
处理表格 10:
表格大小: 8行 x 2列
表格 10 处理完成，无替换
处理表格 11:
表格大小: 8行 x 2列
表格 11 处理完成，无替换
处理表格 12:
表格大小: 5行 x 7列
处理单元格 [2,2]: '${D3范围1}'
    找到匹配: ${D3范围1} -> A批次产品隔离
    替换结果: '${D3范围1}' -> 'A批次产品隔离'
处理单元格 [2,3]: '${D3处置对策1}'
    找到匹配: ${D3处置对策1} -> 立即停机检查设备、启动追溯流程
    替换结果: '${D3处置对策1}' -> '立即停机检查设备、启动追溯流程'
处理单元格 [2,4]: '${D3责任人1}'
    找到匹配: ${D3责任人1} -> 生产主管王五
    替换结果: '${D3责任人1}' -> '生产主管王五'
处理单元格 [2,5]: '${D3完成期限1}'
    找到匹配: ${D3完成期限1} -> 2023-11-12
    替换结果: '${D3完成期限1}' -> '2023-11-12'
处理单元格 [2,6]: '${D3状态1}'
    找到匹配: ${D3状态1} -> 已完成
    替换结果: '${D3状态1}' -> '已完成'
处理单元格 [2,7]: '${D3进度备注1}'
    找到匹配: ${D3进度备注1} -> 已隔离问题产品并通知客户
    替换结果: '${D3进度备注1}' -> '已隔离问题产品并通知客户'
处理单元格 [3,2]: '${D3范围2}'
    删除未匹配的占位符: ['${D3范围2}']
    替换结果: '${D3范围2}' -> ''
处理单元格 [3,3]: '${D3处置对策2}'
    删除未匹配的占位符: ['${D3处置对策2}']
    替换结果: '${D3处置对策2}' -> ''
处理单元格 [3,4]: '${D3责任人2}'
    删除未匹配的占位符: ['${D3责任人2}']
    替换结果: '${D3责任人2}' -> ''
处理单元格 [3,5]: '${D3完成期限2}'
    删除未匹配的占位符: ['${D3完成期限2}']
    替换结果: '${D3完成期限2}' -> ''
处理单元格 [3,6]: '${D3状态2}'
    删除未匹配的占位符: ['${D3状态2}']
    替换结果: '${D3状态2}' -> ''
处理单元格 [3,7]: '${D3进度备注2}'
    删除未匹配的占位符: ['${D3进度备注2}']
    替换结果: '${D3进度备注2}' -> ''
处理单元格 [4,2]: '${D3范围3}'
    删除未匹配的占位符: ['${D3范围3}']
    替换结果: '${D3范围3}' -> ''
处理单元格 [4,3]: '${D3处置对策3}'
    删除未匹配的占位符: ['${D3处置对策3}']
    替换结果: '${D3处置对策3}' -> ''
处理单元格 [4,4]: '${D3责任人3}'
    删除未匹配的占位符: ['${D3责任人3}']
    替换结果: '${D3责任人3}' -> ''
处理单元格 [4,5]: '${D3完成期限3}'
    删除未匹配的占位符: ['${D3完成期限3}']
    替换结果: '${D3完成期限3}' -> ''
处理单元格 [4,6]: '${D3状态3}'
    删除未匹配的占位符: ['${D3状态3}']
    替换结果: '${D3状态3}' -> ''
处理单元格 [4,7]: '${D3进度备注3}'
    删除未匹配的占位符: ['${D3进度备注3}']
    替换结果: '${D3进度备注3}' -> ''
处理单元格 [5,2]: '${D3范围4}'
    删除未匹配的占位符: ['${D3范围4}']
    替换结果: '${D3范围4}' -> ''
处理单元格 [5,3]: '${D3处置对策4}'
    删除未匹配的占位符: ['${D3处置对策4}']
    替换结果: '${D3处置对策4}' -> ''
处理单元格 [5,4]: '${D3责任人4}'
    删除未匹配的占位符: ['${D3责任人4}']
    替换结果: '${D3责任人4}' -> ''
处理单元格 [5,5]: '${D3完成期限4}'
    删除未匹配的占位符: ['${D3完成期限4}']
    替换结果: '${D3完成期限4}' -> ''
处理单元格 [5,6]: '${D3状态4}'
    删除未匹配的占位符: ['${D3状态4}']
    替换结果: '${D3状态4}' -> ''
处理单元格 [5,7]: '${D3进度备注4}'
    删除未匹配的占位符: ['${D3进度备注4}']
    替换结果: '${D3进度备注4}' -> ''
表格 12 处理完成，有替换
处理表格 13:
表格大小: 5行 x 7列
表格 13 处理完成，无替换
处理表格 14:
表格大小: 5行 x 7列
表格 14 处理完成，无替换
处理表格 15:
表格大小: 5行 x 7列
表格 15 处理完成，无替换
处理表格 16:
表格大小: 5行 x 7列
表格 16 处理完成，无替换
处理表格 17:
表格大小: 6行 x 3列
处理单元格 [2,2]: '${D4why1}'
    找到匹配: ${D4why1} -> 为何有划痕？
    替换结果: '${D4why1}' -> '为何有划痕？'
处理单元格 [2,3]: '${D4answer1}'
    找到匹配: ${D4answer1} -> 设备导辊磨损
    替换结果: '${D4answer1}' -> '设备导辊磨损'
处理单元格 [3,2]: '${D4why2}'
    找到匹配: ${D4why2} -> 为何导辊磨损？
    替换结果: '${D4why2}' -> '为何导辊磨损？'
处理单元格 [3,3]: '${D4answer2}'
    找到匹配: ${D4answer2} -> 未按周期维护
    替换结果: '${D4answer2}' -> '未按周期维护'
处理单元格 [4,2]: '${D4why3}'
    找到匹配: ${D4why3} -> 为何未维护？
    替换结果: '${D4why3}' -> '为何未维护？'
处理单元格 [4,3]: '${D4answer3}'
    找到匹配: ${D4answer3} -> 未制定维护计划
    替换结果: '${D4answer3}' -> '未制定维护计划'
处理单元格 [5,2]: '${D4why4}'
    找到匹配: ${D4why4} -> 为何无计划？
    替换结果: '${D4why4}' -> '为何无计划？'
处理单元格 [5,3]: '${D4answer4}'
    找到匹配: ${D4answer4} -> 设备管理制度缺失
    替换结果: '${D4answer4}' -> '设备管理制度缺失'
处理单元格 [6,2]: '${D4why5}'
    找到匹配: ${D4why5} -> 为何制度缺失？
    替换结果: '${D4why5}' -> '为何制度缺失？'
处理单元格 [6,3]: '${D4answer5}'
    找到匹配: ${D4answer5} -> 未识别此风险
    替换结果: '${D4answer5}' -> '未识别此风险'
表格 17 处理完成，有替换
处理表格 18:
表格大小: 8行 x 4列
处理单元格 [2,2]: '${D4可能原因1}'
    找到匹配: ${D4可能原因1} -> 设备维护缺失
    替换结果: '${D4可能原因1}' -> '设备维护缺失'
处理单元格 [2,3]: '${D4判定1}'
    找到匹配: ${D4判定1} -> 是
    替换结果: '${D4判定1}' -> '是'
处理单元格 [2,4]: '${D4证据1}'
    找到匹配: ${D4证据1} -> 设备记录无维护记录
    替换结果: '${D4证据1}' -> '设备记录无维护记录'
处理单元格 [3,2]: '${D4可能原因2}'
    找到匹配: ${D4可能原因2} -> 操作培训不足
    替换结果: '${D4可能原因2}' -> '操作培训不足'
处理单元格 [3,3]: '${D4判定2}'
    找到匹配: ${D4判定2} -> 是
    替换结果: '${D4判定2}' -> '是'
处理单元格 [3,4]: '${D4证据2}'
    找到匹配: ${D4证据2} -> 员工未通过最新操作考核
    替换结果: '${D4证据2}' -> '员工未通过最新操作考核'
处理单元格 [4,2]: '${D4可能原因3}'
    找到匹配: ${D4可能原因3} -> 
    替换结果: '${D4可能原因3}' -> ''
处理单元格 [4,3]: '${D4判定3}'
    找到匹配: ${D4判定3} -> 
    替换结果: '${D4判定3}' -> ''
处理单元格 [4,4]: '${D4证据3}'
    找到匹配: ${D4证据3} -> 
172.18.0.6 - - [26/Jun/2025 19:19:09] "POST /process_ppt HTTP/1.1" 200 -
    替换结果: '${D4证据3}' -> ''
处理单元格 [5,2]: '${D4可能原因4}'
    删除未匹配的占位符: ['${D4可能原因4}']
    替换结果: '${D4可能原因4}' -> ''
处理单元格 [5,3]: '${D4判定4}'
    删除未匹配的占位符: ['${D4判定4}']
    替换结果: '${D4判定4}' -> ''
处理单元格 [5,4]: '${D4证据4}'
    删除未匹配的占位符: ['${D4证据4}']
    替换结果: '${D4证据4}' -> ''
处理单元格 [6,2]: '${D4可能原因5}'
    删除未匹配的占位符: ['${D4可能原因5}']
    替换结果: '${D4可能原因5}' -> ''
处理单元格 [6,3]: '${D4判定5}'
    删除未匹配的占位符: ['${D4判定5}']
    替换结果: '${D4判定5}' -> ''
处理单元格 [6,4]: '${D4证据5}'
    删除未匹配的占位符: ['${D4证据5}']
    替换结果: '${D4证据5}' -> ''
处理单元格 [7,2]: '${D4可能原因6}'
    删除未匹配的占位符: ['${D4可能原因6}']
    替换结果: '${D4可能原因6}' -> ''
处理单元格 [7,3]: '${D4判定6}'
    删除未匹配的占位符: ['${D4判定6}']
    替换结果: '${D4判定6}' -> ''
处理单元格 [7,4]: '${D4证据6}'
    删除未匹配的占位符: ['${D4证据6}']
    替换结果: '${D4证据6}' -> ''
处理单元格 [8,2]: '${D4可能原因7}'
    删除未匹配的占位符: ['${D4可能原因7}']
    替换结果: '${D4可能原因7}' -> ''
处理单元格 [8,3]: '${D4判定7}'
    删除未匹配的占位符: ['${D4判定7}']
    替换结果: '${D4判定7}' -> ''
处理单元格 [8,4]: '${D4证据7}'
    删除未匹配的占位符: ['${D4证据7}']
    替换结果: '${D4证据7}' -> ''
表格 18 处理完成，有替换
处理表格 19:
表格大小: 6行 x 3列
表格 19 处理完成，无替换
处理表格 20:
表格大小: 8行 x 4列
表格 20 处理完成，无替换
处理表格 21:
表格大小: 6行 x 3列
表格 21 处理完成，无替换
处理表格 22:
表格大小: 8行 x 4列
表格 22 处理完成，无替换
处理表格 23:
表格大小: 6行 x 3列
表格 23 处理完成，无替换
处理表格 24:
表格大小: 8行 x 4列
表格 24 处理完成，无替换
处理表格 25:
表格大小: 6行 x 3列
表格 25 处理完成，无替换
处理表格 26:
表格大小: 8行 x 4列
表格 26 处理完成，无替换
处理表格 27:
表格大小: 5行 x 4列
处理单元格 [2,2]: '${D5纠正措施1}'
    找到匹配: ${D5纠正措施1} -> 制定设备维护计划并培训操作员
    替换结果: '${D5纠正措施1}' -> '制定设备维护计划并培训操作员'
处理单元格 [2,3]: '${D5责任人1}'
    找到匹配: ${D5责任人1} -> 技术工程师赵六
    替换结果: '${D5责任人1}' -> '技术工程师赵六'
处理单元格 [2,4]: '${D5计划完成日期1}'
    找到匹配: ${D5计划完成日期1} -> 2023-12-01
    替换结果: '${D5计划完成日期1}' -> '2023-12-01'
处理单元格 [3,2]: '${D5纠正措施2}'
    删除未匹配的占位符: ['${D5纠正措施2}']
    替换结果: '${D5纠正措施2}' -> ''
处理单元格 [3,3]: '${D5责任人2}'
    删除未匹配的占位符: ['${D5责任人2}']
    替换结果: '${D5责任人2}' -> ''
处理单元格 [3,4]: '${D5计划完成日期2}'
    删除未匹配的占位符: ['${D5计划完成日期2}']
    替换结果: '${D5计划完成日期2}' -> ''
处理单元格 [4,2]: '${D5纠正措施3}'
    删除未匹配的占位符: ['${D5纠正措施3}']
    替换结果: '${D5纠正措施3}' -> ''
处理单元格 [4,3]: '${D5责任人3}'
    删除未匹配的占位符: ['${D5责任人3}']
    替换结果: '${D5责任人3}' -> ''
处理单元格 [4,4]: '${D5计划完成日期3}'
    删除未匹配的占位符: ['${D5计划完成日期3}']
    替换结果: '${D5计划完成日期3}' -> ''
处理单元格 [5,2]: '${D5纠正措施4}'
    删除未匹配的占位符: ['${D5纠正措施4}']
    替换结果: '${D5纠正措施4}' -> ''
处理单元格 [5,3]: '${D5责任人4}'
    删除未匹配的占位符: ['${D5责任人4}']
    替换结果: '${D5责任人4}' -> ''
处理单元格 [5,4]: '${D5计划完成日期4}'
    删除未匹配的占位符: ['${D5计划完成日期4}']
    替换结果: '${D5计划完成日期4}' -> ''
表格 27 处理完成，有替换
处理表格 28:
表格大小: 5行 x 4列
表格 28 处理完成，无替换
处理表格 29:
表格大小: 5行 x 4列
表格 29 处理完成，无替换
处理表格 30:
表格大小: 5行 x 4列
表格 30 处理完成，无替换
处理表格 31:
表格大小: 5行 x 4列
表格 31 处理完成，无替换
处理表格 32:
表格大小: 5行 x 5列
处理单元格 [2,2]: '${D6措施验证1}'
    找到匹配: ${D6措施验证1} -> 抽样检测200件产品划痕率
    替换结果: '${D6措施验证1}' -> '抽样检测200件产品划痕率'
处理单元格 [2,3]: '${D6验证人1}'
    找到匹配: ${D6验证人1} -> 质检员陈七
    替换结果: '${D6验证人1}' -> '质检员陈七'
处理单元格 [2,4]: '${D6验证时间1}'
    找到匹配: ${D6验证时间1} -> 2023-12-05
    替换结果: '${D6验证时间1}' -> '2023-12-05'
处理单元格 [2,5]: '${D6验证结果1}'
    找到匹配: ${D6验证结果1} -> 划痕率降至0.5%以下，符合标准
    替换结果: '${D6验证结果1}' -> '划痕率降至0.5%以下，符合标准'
处理单元格 [3,2]: '${D6措施验证2}'
    删除未匹配的占位符: ['${D6措施验证2}']
    替换结果: '${D6措施验证2}' -> ''
处理单元格 [3,3]: '${D6验证人2}'
    删除未匹配的占位符: ['${D6验证人2}']
    替换结果: '${D6验证人2}' -> ''
处理单元格 [3,4]: '${D6验证时间2}'
    删除未匹配的占位符: ['${D6验证时间2}']
    替换结果: '${D6验证时间2}' -> ''
处理单元格 [3,5]: '${D6验证结果2}'
    删除未匹配的占位符: ['${D6验证结果2}']
    替换结果: '${D6验证结果2}' -> ''
处理单元格 [4,2]: '${D6措施验证3}'
    删除未匹配的占位符: ['${D6措施验证3}']
    替换结果: '${D6措施验证3}' -> ''
处理单元格 [4,3]: '${D6验证人3}'
    删除未匹配的占位符: ['${D6验证人3}']
    替换结果: '${D6验证人3}' -> ''
处理单元格 [4,4]: '${D6验证时间3}'
    删除未匹配的占位符: ['${D6验证时间3}']
    替换结果: '${D6验证时间3}' -> ''
处理单元格 [4,5]: '${D6验证结果3}'
    删除未匹配的占位符: ['${D6验证结果3}']
    替换结果: '${D6验证结果3}' -> ''
处理单元格 [5,2]: '${D6措施验证4}'
    删除未匹配的占位符: ['${D6措施验证4}']
    替换结果: '${D6措施验证4}' -> ''
处理单元格 [5,3]: '${D6验证人4}'
    删除未匹配的占位符: ['${D6验证人4}']
    替换结果: '${D6验证人4}' -> ''
处理单元格 [5,4]: '${D6验证时间4}'
    删除未匹配的占位符: ['${D6验证时间4}']
    替换结果: '${D6验证时间4}' -> ''
处理单元格 [5,5]: '${D6验证结果4}'
    删除未匹配的占位符: ['${D6验证结果4}']
    替换结果: '${D6验证结果4}' -> ''
表格 32 处理完成，有替换
处理表格 33:
表格大小: 5行 x 5列
表格 33 处理完成，无替换
处理表格 34:
表格大小: 5行 x 5列
表格 34 处理完成，无替换
处理表格 35:
表格大小: 5行 x 5列
表格 35 处理完成，无替换
处理表格 36:
表格大小: 5行 x 5列
表格 36 处理完成，无替换
处理表格 37:
表格大小: 5行 x 4列
处理单元格 [2,2]: '${D7预防措施1}'
    找到匹配: ${D7预防措施1} -> 更新设备维护SOP、增加操作员月度考核、安装设备磨损预警传感器
    替换结果: '${D7预防措施1}' -> '更新设备维护SOP、增加操作员月度考核、安装设备磨损预警传感器'
处理单元格 [2,3]: '${D7责任人1}'
    找到匹配: ${D7责任人1} -> 质量部经理李四
    替换结果: '${D7责任人1}' -> '质量部经理李四'
处理单元格 [2,4]: '${D7计划完成日期1}'
    找到匹配: ${D7计划完成日期1} -> 2023-11-30
    替换结果: '${D7计划完成日期1}' -> '2023-11-30'
处理单元格 [3,2]: '${D7预防措施2}'
    删除未匹配的占位符: ['${D7预防措施2}']
172.18.0.6 - - [26/Jun/2025 19:19:09] "POST /process_docx HTTP/1.1" 200 -
    替换结果: '${D7预防措施2}' -> ''
处理单元格 [3,3]: '${D7责任人2}'
    删除未匹配的占位符: ['${D7责任人2}']
    替换结果: '${D7责任人2}' -> ''
处理单元格 [3,4]: '${D7计划完成日期2}'
    删除未匹配的占位符: ['${D7计划完成日期2}']
    替换结果: '${D7计划完成日期2}' -> ''
处理单元格 [4,2]: '${D7预防措施3}'
    删除未匹配的占位符: ['${D7预防措施3}']
    替换结果: '${D7预防措施3}' -> ''
处理单元格 [4,3]: '${D7责任人3}'
    删除未匹配的占位符: ['${D7责任人3}']
    替换结果: '${D7责任人3}' -> ''
处理单元格 [4,4]: '${D7计划完成日期3}'
    删除未匹配的占位符: ['${D7计划完成日期3}']
    替换结果: '${D7计划完成日期3}' -> ''
处理单元格 [5,2]: '${D7预防措施4}'
    删除未匹配的占位符: ['${D7预防措施4}']
    替换结果: '${D7预防措施4}' -> ''
处理单元格 [5,3]: '${D7责任人4}'
    删除未匹配的占位符: ['${D7责任人4}']
    替换结果: '${D7责任人4}' -> ''
处理单元格 [5,4]: '${D7计划完成日期4}'
    删除未匹配的占位符: ['${D7计划完成日期4}']
    替换结果: '${D7计划完成日期4}' -> ''
表格 37 处理完成，有替换
处理表格 38:
表格大小: 5行 x 4列
表格 38 处理完成，无替换
处理表格 39:
表格大小: 5行 x 4列
表格 39 处理完成，无替换
处理表格 40:
表格大小: 5行 x 4列
表格 40 处理完成，无替换
处理表格 41:
表格大小: 5行 x 4列
表格 41 处理完成，无替换
DOCX处理完成
文件已保存: generated_docx/20250626_191909_73de924c.docx

加载DOCX文档: template.docx
扁平化后的替换数据: {'${D0标题}': '30号机4组抛头晶圆加工去除量不一致问题', '${D0汇报人}': '技术部', '${D0汇报时间}': '2024-05-30', '${D1组长姓名}': '黄金涛', '${D1组长部门}': '研发部', '${D1组长职位}': '技术负责人', '${D1组长主要职责}': '负责原因分析与方案设计', '${D1成员1姓名}': '马镇泽', '${D1成员1部门}': '生产部', '${D1成员1职位}': '安装工艺优化', '${D1成员1主要职责}': '', '${D1成员2姓名}': '莫易栋', '${D1成员2部门}': '质量部', '${D1成员2职位}': '检验标准制定', '${D1成员2主要职责}': '', '${D1成员3姓名}': '厉炜栋', '${D1成员3部门}': '设备部', '${D1成员3职位}': '压力系统调试', '${D1成员3主要职责}': '', '${D2事件整体描述}': '2024-04-01/宜兴中环客户端/客户端售后反馈/去除量波动超出工艺要求/30号机4组抛头晶圆加工去除量波动100-110nm（要求≤60nm）/抛头加压过程中弹簧板翘曲、下抛头GAP值及重量不一致导致压力波动/影响晶圆加工质量，严重程度一般，发生1次', '${D2何时发生}': '2024-04-01', '${D2何地发生}': '宜兴中环客户端', '${D2何人发现}': '客户端售后', '${D2为什么是这问题}': '去除量波动超出工艺要求', '${D2发生了什么问题}': '30号机4组抛头晶圆加工去除量波动100-110nm（要求≤60nm）', '${D2问题如何发生}': '抛头加压过程中弹簧板翘曲、下抛头GAP值及重量不一致导致压力波动', '${D2问题影响程度}': '影响晶圆加工质量，严重程度一般，发生1次', '${D3范围1}': '在制（客户端）', '${D3处置对策1}': '调整GAP值使抛头压力一致', '${D3责任人1}': '彭志远', '${D3完成期限1}': '2024-04-01', '${D3状态1}': '执行完成', '${D3进度备注1}': '去除量波动达标', '${D3范围2}': '在途（同型号机台）', '${D3处置对策2}': '横向排查压力一致性', '${D3责任人2}': '马镇泽,历炜栋,王宝林', '${D3完成期限2}': '2024-04-01', '${D3状态2}': '执行中', '${D3进度备注2}': '100%机台存在压力波动异常', '${D4why1}': '为什么去除量波动大？', '${D4answer1}': '抛头压力波动异常', '${D4why2}': '为什么压力波动异常？', '${D4answer2}': '弹簧板翘曲及下抛头GAP/重量不一致', '${D4why3}': '为什么GAP/重量不一致？', '${D4answer3}': '抛头组件公差未约束', '${D4why4}': '为什么公差未约束？', '${D4answer4}': '设计图纸未规定平面度及重量公差', '${D4why5}': '为什么未规定？', '${D4answer5}': '未识别加工去除量对公差的敏感性', '${D4人原因1}': '安装操作符合SOP', '${D4人原因2}': '', '${D4人原因3}': '', '${D4机原因1}': '工具符合要求', '${D4机原因2}': '', '${D4机原因3}': '', '${D4料原因1}': '弹簧板平面度未约束', '${D4料原因2}': '下抛头GAP/重量公差未约束', '${D4料原因3}': '', '${D4法原因1}': '安装方法符合设计', '${D4法原因2}': '', '${D4法原因3}': '', '${D4环原因1}': '气压符合要求', '${D4环原因2}': '', '${D4环原因3}': '', '${D4测原因1}': '检验方法一致', '${D4测原因2}': '', '${D4测原因3}': '', '${D4可能原因1}': '弹簧板平面度不良', '${D4判定1}': '是', '${D4证据1}': '压力测试显示波动异常，零件检测数据超标', '${D4可能原因2}': '下抛头GAP/重量公差过大', '${D4判定2}': '是', '${D4证据2}': '压力测试显示波动异常，零件检测数据超标', '${D4可能原因3}': '', '${D4判定3}': '', '${D4证据3}': '', '${D4原因小结}': '主因：弹簧板平面度不良、下抛头GAP/重量公差过大；证据：压力测试显示波动异常，零件检测数据超标', '${D5纠正措施1}': '修订弹簧板平面度要求至0.1mm，约束抛头固定环/浮动基座尺寸重量公差', '${D5责任人1}': '黄金涛,马镇泽,莫易栋,厉炜栋', '${D5计划完成日期1}': '2024-04-02至2024-05-30', '${D6措施验证1}': '更换平面度0.1mm弹簧板及公差约束组件并测试', '${D6验证人1}': '厉炜栋,彭志远,祝康康', '${D6验证时间1}': '2024-05-30', '${D6验证结果1}': '去除量波动≤60nm，压力波动≤±1.2N', '${D7预防措施1}': '修订弹簧板图纸增加平面度要求，更新抛头组件公差标准至SOP', '${D7责任人1}': '黄金涛', '${D7计划完成日期1}': '2024-05-05', '${D8有效性确认}': '', '${D8确认人}': '', '${D8确认完成时间}': ''}
开始处理DOCX中的所有占位符...
处理文档段落...
找到 41 个表格（包括嵌套表格）
处理表格 1:
表格大小: 20行 x 5列
处理单元格 [8,1]: '事件整体描述：${D2事件整体描述}'
    找到匹配: ${D2事件整体描述} -> 2024-04-01/宜兴中环客户端/客户端售后反馈/去除量波动超出工艺要求/30号机4组抛头晶圆加工去除量波动100-110nm（要求≤60nm）/抛头加压过程中弹簧板翘曲、下抛头GAP值及重量不一致导致压力波动/影响晶圆加工质量，严重程度一般，发生1次
    替换结果: '事件整体描述：${D2事件整体描述}' -> '事件整体描述：2024-04-01/宜兴中环客户端/客户端售后反馈/去除量波动超出工艺要求/30号机4组抛头晶圆加工去除量波动100-110nm（要求≤60nm）/抛头加压过程中弹簧板翘曲、下抛头GAP值及重量不一致导致压力波动/影响晶圆加工质量，严重程度一般，发生1次'
处理单元格 [12,1]: '5Why分析：
产生原因小结：${D4原因小结}'
    找到匹配: ${D4原因小结} -> 主因：弹簧板平面度不良、下抛头GAP/重量公差过大；证据：压力测试显示波动异常，零件检测数据超标
    替换结果: '产生原因小结：${D4原因小结} ' -> '产生原因小结：主因：弹簧板平面度不良、下抛头GAP/重量公差过大；证据：压力测试显示波动异常，零件检测数据超标 '
处理单元格 [20,1]: '确认文件内容及有效性：
${D8有效性确认}
关闭确认人/日期：${D8确认人}  ${D8确认完成时间}'
    找到匹配: ${D8有效性确认} -> 
    替换结果: '${D8有效性确认}' -> ''
    找到匹配: ${D8确认人} -> 
    找到匹配: ${D8确认完成时间} -> 
    替换结果: '关闭确认人/日期：${D8确认人}  ${D8确认完成时间}' -> '关闭确认人/日期：  '
表格 1 处理完成，有替换
处理表格 2:
表格大小: 8行 x 5列
处理单元格 [2,2]: '${D1组长姓名}'
    找到匹配: ${D1组长姓名} -> 黄金涛
    替换结果: '${D1组长姓名}' -> '黄金涛'
处理单元格 [2,3]: '${D1组长部门}'
    找到匹配: ${D1组长部门} -> 研发部
    替换结果: '${D1组长部门}' -> '研发部'
处理单元格 [2,4]: '${D1组长职位}'
    找到匹配: ${D1组长职位} -> 技术负责人
    替换结果: '${D1组长职位}' -> '技术负责人'
处理单元格 [2,5]: '${D1组长主要职责}'
    找到匹配: ${D1组长主要职责} -> 负责原因分析与方案设计
    替换结果: '${D1组长主要职责}' -> '负责原因分析与方案设计'
处理单元格 [3,2]: '${D1成员1姓名}'
    找到匹配: ${D1成员1姓名} -> 马镇泽
    替换结果: '${D1成员1姓名}' -> '马镇泽'
处理单元格 [3,3]: '${D1成员1部门}'
    找到匹配: ${D1成员1部门} -> 生产部
    替换结果: '${D1成员1部门}' -> '生产部'
处理单元格 [3,4]: '${D1成员1职位}'
    找到匹配: ${D1成员1职位} -> 安装工艺优化
    替换结果: '${D1成员1职位}' -> '安装工艺优化'
处理单元格 [3,5]: '${D1成员1主要职责}'
    找到匹配: ${D1成员1主要职责} -> 
    替换结果: '${D1成员1主要职责}' -> ''
处理单元格 [4,2]: '${D1成员2姓名}'
    找到匹配: ${D1成员2姓名} -> 莫易栋
    替换结果: '${D1成员2姓名}' -> '莫易栋'
处理单元格 [4,3]: '${D1成员2部门}'
    找到匹配: ${D1成员2部门} -> 质量部
    替换结果: '${D1成员2部门}' -> '质量部'
处理单元格 [4,4]: '${D1成员2职位}'
    找到匹配: ${D1成员2职位} -> 检验标准制定
    替换结果: '${D1成员2职位}' -> '检验标准制定'
处理单元格 [4,5]: '${D1成员2主要职责}'
    找到匹配: ${D1成员2主要职责} -> 
    替换结果: '${D1成员2主要职责}' -> ''
处理单元格 [5,2]: '${D1成员3姓名}'
    找到匹配: ${D1成员3姓名} -> 厉炜栋
    替换结果: '${D1成员3姓名}' -> '厉炜栋'
处理单元格 [5,3]: '${D1成员3部门}'
    找到匹配: ${D1成员3部门} -> 设备部
    替换结果: '${D1成员3部门}' -> '设备部'
处理单元格 [5,4]: '${D1成员3职位}'
    找到匹配: ${D1成员3职位} -> 压力系统调试
    替换结果: '${D1成员3职位}' -> '压力系统调试'
处理单元格 [5,5]: '${D1成员3主要职责}'
    找到匹配: ${D1成员3主要职责} -> 
    替换结果: '${D1成员3主要职责}' -> ''
处理单元格 [6,2]: '${D1成员4姓名}'
    删除未匹配的占位符: ['${D1成员4姓名}']
    替换结果: '${D1成员4姓名}' -> ''
处理单元格 [6,3]: '${D1成员4部门}'
    删除未匹配的占位符: ['${D1成员4部门}']
    替换结果: '${D1成员4部门}' -> ''
处理单元格 [6,4]: '${D1成员4职位}'
    删除未匹配的占位符: ['${D1成员4职位}']
    替换结果: '${D1成员4职位}' -> ''
处理单元格 [6,5]: '${D1成员4主要职责}'
    删除未匹配的占位符: ['${D1成员4主要职责}']
    替换结果: '${D1成员4主要职责}' -> ''
处理单元格 [7,2]: '${D1成员5姓名}'
    删除未匹配的占位符: ['${D1成员5姓名}']
    替换结果: '${D1成员5姓名}' -> ''
处理单元格 [7,3]: '${D1成员5部门}'
    删除未匹配的占位符: ['${D1成员5部门}']
    替换结果: '${D1成员5部门}' -> ''
处理单元格 [7,4]: '${D1成员5职位}'
    删除未匹配的占位符: ['${D1成员5职位}']
    替换结果: '${D1成员5职位}' -> ''
处理单元格 [7,5]: '${D1成员5主要职责}'
    删除未匹配的占位符: ['${D1成员5主要职责}']
    替换结果: '${D1成员5主要职责}' -> ''
处理单元格 [8,2]: '${D1成员6姓名}'
    删除未匹配的占位符: ['${D1成员6姓名}']
    替换结果: '${D1成员6姓名}' -> ''
处理单元格 [8,3]: '${D1成员6部门}'
    删除未匹配的占位符: ['${D1成员6部门}']
    替换结果: '${D1成员6部门}' -> ''
处理单元格 [8,4]: '${D1成员6职位}'
    删除未匹配的占位符: ['${D1成员6职位}']
    替换结果: '${D1成员6职位}' -> ''
处理单元格 [8,5]: '${D1成员6主要职责}'
扁平化后的替换数据: {'${D0标题}': '30号机4组抛头晶圆加工去除量不一致问题', '${D0汇报人}': '技术部', '${D0汇报时间}': '2024-05-30', '${D1组长姓名}': '黄金涛', '${D1组长部门}': '研发部', '${D1组长职位}': '技术负责人', '${D1组长主要职责}': '负责原因分析与方案设计', '${D1成员1姓名}': '马镇泽', '${D1成员1部门}': '生产部', '${D1成员1职位}': '安装工艺优化', '${D1成员1主要职责}': '', '${D1成员2姓名}': '莫易栋', '${D1成员2部门}': '质量部', '${D1成员2职位}': '检验标准制定', '${D1成员2主要职责}': '', '${D1成员3姓名}': '厉炜栋', '${D1成员3部门}': '设备部', '${D1成员3职位}': '压力系统调试', '${D1成员3主要职责}': '', '${D2事件整体描述}': '2024-04-01/宜兴中环客户端/客户端售后反馈/去除量波动超出工艺要求/30号机4组抛头晶圆加工去除量波动100-110nm（要求≤60nm）/抛头加压过程中弹簧板翘曲、下抛头GAP值及重量不一致导致压力波动/影响晶圆加工质量，严重程度一般，发生1次', '${D2何时发生}': '2024-04-01', '${D2何地发生}': '宜兴中环客户端', '${D2何人发现}': '客户端售后', '${D2为什么是这问题}': '去除量波动超出工艺要求', '${D2发生了什么问题}': '30号机4组抛头晶圆加工去除量波动100-110nm（要求≤60nm）', '${D2问题如何发生}': '抛头加压过程中弹簧板翘曲、下抛头GAP值及重量不一致导致压力波动', '${D2问题影响程度}': '影响晶圆加工质量，严重程度一般，发生1次', '${D3范围1}': '在制（客户端）', '${D3处置对策1}': '调整GAP值使抛头压力一致', '${D3责任人1}': '彭志远', '${D3完成期限1}': '2024-04-01', '${D3状态1}': '执行完成', '${D3进度备注1}': '去除量波动达标', '${D3范围2}': '在途（同型号机台）', '${D3处置对策2}': '横向排查压力一致性', '${D3责任人2}': '马镇泽,历炜栋,王宝林', '${D3完成期限2}': '2024-04-01', '${D3状态2}': '执行中', '${D3进度备注2}': '100%机台存在压力波动异常', '${D4why1}': '为什么去除量波动大？', '${D4answer1}': '抛头压力波动异常', '${D4why2}': '为什么压力波动异常？', '${D4answer2}': '弹簧板翘曲及下抛头GAP/重量不一致', '${D4why3}': '为什么GAP/重量不一致？', '${D4answer3}': '抛头组件公差未约束', '${D4why4}': '为什么公差未约束？', '${D4answer4}': '设计图纸未规定平面度及重量公差', '${D4why5}': '为什么未规定？', '${D4answer5}': '未识别加工去除量对公差的敏感性', '${D4人原因1}': '安装操作符合SOP', '${D4人原因2}': '', '${D4人原因3}': '', '${D4机原因1}': '工具符合要求', '${D4机原因2}': '', '${D4机原因3}': '', '${D4料原因1}': '弹簧板平面度未约束', '${D4料原因2}': '下抛头GAP/重量公差未约束', '${D4料原因3}': '', '${D4法原因1}': '安装方法符合设计', '${D4法原因2}': '', '${D4法原因3}': '', '${D4环原因1}': '气压符合要求', '${D4环原因2}': '', '${D4环原因3}': '', '${D4测原因1}': '检验方法一致', '${D4测原因2}': '', '${D4测原因3}': '', '${D4可能原因1}': '弹簧板平面度不良', '${D4判定1}': '是', '${D4证据1}': '压力测试显示波动异常，零件检测数据超标', '${D4可能原因2}': '下抛头GAP/重量公差过大', '${D4判定2}': '是', '${D4证据2}': '压力测试显示波动异常，零件检测数据超标', '${D4可能原因3}': '', '${D4判定3}': '', '${D4证据3}': '', '${D4原因小结}': '主因：弹簧板平面度不良、下抛头GAP/重量公差过大；证据：压力测试显示波动异常，零件检测数据超标', '${D5纠正措施1}': '修订弹簧板平面度要求至0.1mm，约束抛头固定环/浮动基座尺寸重量公差', '${D5责任人1}': '黄金涛,马镇泽,莫易栋,厉炜栋', '${D5计划完成日期1}': '2024-04-02至2024-05-30', '${D6措施验证1}': '更换平面度0.1mm弹簧板及公差约束组件并测试', '${D6验证人1}': '厉炜栋,彭志远,祝康康', '${D6验证时间1}': '2024-05-30', '${D6验证结果1}': '去除量波动≤60nm，压力波动≤±1.2N', '${D7预防措施1}': '修订弹簧板图纸增加平面度要求，更新抛头组件公差标准至SOP', '${D7责任人1}': '黄金涛', '${D7计划完成日期1}': '2024-05-05', '${D8有效性确认}': '', '${D8确认人}': '', '${D8确认完成时间}': ''}
开始处理PPT中的所有占位符...
处理第 1 页幻灯片...
    删除未匹配的占位符: ['${D1成员6主要职责}']
    替换结果: '${D1成员6主要职责}' -> ''
  找到占位符: ['${D0标题}', '${D0汇报时间}', '${D0汇报人}']
  发现text_frame形状
    处理段落文本: '${D0标题}...'
      替换: ${D0标题} -> 30号机4组抛头晶圆加工去除量不一致问题
      段落已更新: '30号机4组抛头晶圆加工去除量不一致问题...'
表格 2 处理完成，有替换
处理表格 3:
表格大小: 8行 x 5列
  发现text_frame形状
    处理段落文本: '汇报人：${D0汇报人}...'
      替换: ${D0汇报人} -> 技术部
      段落已更新: '汇报人：技术部...'
    处理段落文本: '汇报时间：${D0汇报时间}...'
      替换: ${D0汇报时间} -> 2024-05-30
      段落已更新: '汇报时间：2024-05-30...'
处理第 2 页幻灯片...
  找到占位符: ['${D1成员2主要职责}', '${D1成员2部门}', '${D1成员2姓名}', '${D1成员4主要职责}', '${D1成员3姓名}', '${D1成员6主要职责}', '${D1成员3主要职责}', '${D1成员5部门}', '${D1成员4部门}', '${D1成员1姓名}', '${D1组长部门}', '${D1成员3部门}', '${D1组长主要职责}', '${D1成员4职位}', '${D1组长姓名}', '${D1成员4姓名}', '${D1成员2职位}', '${D1成员5姓名}', '${D1成员1部门}', '${D1成员3职位}', '${D1成员6职位}', '${D1成员1主要职责}', '${D1成员6部门}', '${D1成员6姓名}', '${D1组长职位}', '${D1成员5主要职责}', '${D1成员1职位}', '${D1成员5职位}']
处理第 3 页幻灯片...
  找到占位符: ['${D2何时发生}', '${D2为什么是这问题}', '${D2问题如何发生}', '${D2事件整体描述}', '${D2问题影响程度}', '${D2发生了什么问题}', '${D2何人发现}', '${D2何地发生}']
  发现text_frame形状
    处理段落文本: '事件整体描述：${D2事件整体描述}...'
      替换: ${D2事件整体描述} -> 2024-04-01/宜兴中环客户端/客户端售后反馈/去除量波动超出工艺要求/30号机4组抛头晶圆加工去除量波动100-110nm（要求≤60nm）/抛头加压过程中弹簧板翘曲、下抛头GAP值及重量不一致导致压力波动/影响晶圆加工质量，严重程度一般，发生1次
      段落已更新: '事件整体描述：2024-04-01/宜兴中环客户端/客户端售后反馈/去除量波动超出工艺要求/30号机...'
  发现text_frame形状
    处理段落文本: '附图说明...'
处理第 4 页幻灯片...
  找到占位符: ['${D3责任人2}', '${D3范围2}', '${D3完成期限3}', '${D3进度备注2}', '${D3责任人1}', '${D3范围4}', '${D3责任人4}', '${D3完成期限2}', '${D3状态4}', '${D3进度备注4}', '${D3责任人3}', '${D3状态3}', '${D3状态1}', '${D3范围3}', '${D3范围1}', '${D3状态2}', '${D3处置对策3}', '${D3完成期限4}', '${D3处置对策2}', '${D3完成期限1}', '${D3处置对策1}', '${D3进度备注3}', '${D3进度备注1}', '${D3处置对策4}']
表格 3 处理完成，无替换
处理表格 4:
表格大小: 8行 x 5列
处理第 5 页幻灯片...
  发现text_frame形状
    处理段落文本: '5WHY分析...'
  发现组合形状，包含 15 个子形状
  发现text_frame形状
    处理段落文本: '1WHY：${D4why1}...'
      替换: ${D4why1} -> 为什么去除量波动大？
      段落已更新: '1WHY：为什么去除量波动大？...'
  发现text_frame形状
    处理段落文本: '${D4answer1}...'
      替换: ${D4answer1} -> 抛头压力波动异常
      段落已更新: '抛头压力波动异常...'
  发现text_frame形状
    处理段落文本: '${D4answer2}...'
      替换: ${D4answer2} -> 弹簧板翘曲及下抛头GAP/重量不一致
      段落已更新: '弹簧板翘曲及下抛头GAP/重量不一致...'
  发现text_frame形状
    处理段落文本: '${D4answer3}...'
      替换: ${D4answer3} -> 抛头组件公差未约束
      段落已更新: '抛头组件公差未约束...'
  发现text_frame形状
    处理段落文本: '${D4answer4}...'
      替换: ${D4answer4} -> 设计图纸未规定平面度及重量公差
      段落已更新: '设计图纸未规定平面度及重量公差...'
  发现text_frame形状
    处理段落文本: '${D4answer5}...'
      替换: ${D4answer5} -> 未识别加工去除量对公差的敏感性
      段落已更新: '未识别加工去除量对公差的敏感性...'
  发现text_frame形状
    处理段落文本: '2WHY： ${D4why2}...'
      替换: ${D4why2} -> 为什么压力波动异常？
      段落已更新: '2WHY： 为什么压力波动异常？...'
  发现text_frame形状
    处理段落文本: '3WHY： ${D4why3}...'
      替换: ${D4why3} -> 为什么GAP/重量不一致？
      段落已更新: '3WHY： 为什么GAP/重量不一致？...'
  发现text_frame形状
    处理段落文本: '4WHY： ${D4why4}...'
      替换: ${D4why4} -> 为什么公差未约束？
      段落已更新: '4WHY： 为什么公差未约束？...'
  发现text_frame形状
    处理段落文本: '5WHY： ${D4why5}...'
      替换: ${D4why5} -> 为什么未规定？
      段落已更新: '5WHY： 为什么未规定？...'
处理第 6 页幻灯片...
  发现text_frame形状
    处理段落文本: '鱼骨图分析...'
  发现组合形状，包含 2 个子形状
  发现text_frame形状
    处理段落文本: '${D4测原因3}...'
      替换: ${D4测原因3} -> 
      段落已更新: '...'
  发现组合形状，包含 54 个子形状
  发现text_frame形状
  发现text_frame形状
    处理段落文本: '环...'
  发现text_frame形状
    处理段落文本: '法...'
  发现text_frame形状
    处理段落文本: '料...'
  发现text_frame形状
    处理段落文本: '机...'
  发现text_frame形状
    处理段落文本: '人...'
  发现text_frame形状
    处理段落文本: '${D4环原因1}...'
      替换: ${D4环原因1} -> 气压符合要求
      段落已更新: '气压符合要求...'
  发现text_frame形状
    处理段落文本: '${D4环原因2}...'
      替换: ${D4环原因2} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4环原因3}...'
      替换: ${D4环原因3} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4法原因1}...'
      替换: ${D4法原因1} -> 安装方法符合设计
      段落已更新: '安装方法符合设计...'
  发现text_frame形状
    处理段落文本: '${D4法原因3}...'
      替换: ${D4法原因3} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4法原因2}...'
      替换: ${D4法原因2} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4料原因1}...'
      替换: ${D4料原因1} -> 弹簧板平面度未约束
      段落已更新: '弹簧板平面度未约束...'
  发现text_frame形状
    处理段落文本: '${D4料原因2}...'
      替换: ${D4料原因2} -> 下抛头GAP/重量公差未约束
      段落已更新: '下抛头GAP/重量公差未约束...'
  发现text_frame形状
    处理段落文本: '${D4料原因3}...'
      替换: ${D4料原因3} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4机原因1}...'
      替换: ${D4机原因1} -> 工具符合要求
      段落已更新: '工具符合要求...'
  发现text_frame形状
    处理段落文本: '${D4机原因3}...'
      替换: ${D4机原因3} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4机原因2}...'
      替换: ${D4机原因2} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4人原因1}...'
      替换: ${D4人原因1} -> 安装操作符合SOP
      段落已更新: '安装操作符合SOP...'
  发现text_frame形状
    处理段落文本: '${D4人原因2}...'
      替换: ${D4人原因2} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '${D4人原因3}...'
      替换: ${D4人原因3} -> 
      段落已更新: '...'
  发现text_frame形状
    处理段落文本: '测...'
  发现text_frame形状
    处理段落文本: '${D4测原因1}...'
      替换: ${D4测原因1} -> 检验方法一致
      段落已更新: '检验方法一致...'
  发现text_frame形状
    处理段落文本: '${D4测原因2}...'
      替换: ${D4测原因2} -> 
      段落已更新: '...'
  发现text_frame形状
  发现text_frame形状
  发现text_frame形状
  发现text_frame形状
  发现text_frame形状
  发现text_frame形状
处理第 7 页幻灯片...
  找到占位符: ['${D4可能原因1}', '${D4可能原因7}', '${D4证据3}', '${D4证据4}', '${D4判定3}', '${D4可能原因3}', '${D4证据5}', '${D4可能原因5}', '${D4判定4}', '${D4证据1}', '${D4判定5}', '${D4证据2}', '${D4可能原因6}', '${D4判定7}', '${D4判定1}', '${D4原因小结}', '${D4判定6}', '${D4证据7}', '${D4可能原因2}', '${D4判定2}', '${D4可能原因4}', '${D4证据6}']
  发现text_frame形状
    处理段落文本: ' ...'
  发现text_frame形状
    处理段落文本: '小结：${D4原因小结}；...'
      替换: ${D4原因小结} -> 主因：弹簧板平面度不良、下抛头GAP/重量公差过大；证据：压力测试显示波动异常，零件检测数据超标
      段落已更新: '小结：主因：弹簧板平面度不良、下抛头GAP/重量公差过大；证据：压力测试显示波动异常，零件检测数据超...'
  发现text_frame形状
    处理段落文本: '可能原因分析...'
处理第 8 页幻灯片...
  找到占位符: ['${D5责任人2}', '${D5计划完成日期1}', '${D5纠正措施3}', '${D5纠正措施4}', '${D5纠正措施1}', '${D5责任人4}', '${D5责任人1}', '${D5责任人3}', '${D5计划完成日期4}', '${D5计划完成日期3}', '${D5计划完成日期2}', '${D5纠正措施2}']
表格 4 处理完成，无替换
处理表格 5:
表格大小: 8行 x 5列
处理第 9 页幻灯片...
  找到占位符: ['${D6验证结果4}', '${D6验证时间2}', '${D6验证时间3}', '${D6验证人3}', '${D6措施验证4}', '${D6验证时间4}', '${D6验证结果2}', '${D6措施验证2}', '${D6措施验证1}', '${D6验证人2}', '${D6措施验证3}', '${D6验证时间1}', '${D6验证人1}', '${D6验证结果3}', '${D6验证人4}', '${D6验证结果1}']
处理第 10 页幻灯片...
  找到占位符: ['${D7预防措施1}', '${D7责任人3}', '${D7计划完成日期3}', '${D7责任人4}', '${D7计划完成日期4}', '${D7预防措施2}', '${D7计划完成日期2}', '${D7预防措施3}', '${D7责任人2}', '${D7预防措施4}', '${D7计划完成日期1}', '${D7责任人1}']
处理第 11 页幻灯片...
  找到占位符: ['${D8确认完成时间}', '${D8确认人}', '${D8有效性确认}']
处理第 12 页幻灯片...
PPT处理完成
表格 5 处理完成，无替换
处理表格 6:
表格大小: 8行 x 5列
表格 6 处理完成，无替换
处理表格 7:
表格大小: 8行 x 2列
处理单元格 [2,2]: '${D2何时发生}'
    找到匹配: ${D2何时发生} -> 2024-04-01
    替换结果: '${D2何时发生}' -> '2024-04-01'
处理单元格 [3,2]: '${D2何地发生}'
    找到匹配: ${D2何地发生} -> 宜兴中环客户端
    替换结果: '${D2何地发生}' -> '宜兴中环客户端'
处理单元格 [4,2]: '${D2何人发现}'
    找到匹配: ${D2何人发现} -> 客户端售后
    替换结果: '${D2何人发现}' -> '客户端售后'
处理单元格 [5,2]: '${D2为什么是这问题}'
    找到匹配: ${D2为什么是这问题} -> 去除量波动超出工艺要求
    替换结果: '${D2为什么是这问题}' -> '去除量波动超出工艺要求'
处理单元格 [6,2]: '${D2发生了什么问题}'
    找到匹配: ${D2发生了什么问题} -> 30号机4组抛头晶圆加工去除量波动100-110nm（要求≤60nm）
    替换结果: '${D2发生了什么问题}' -> '30号机4组抛头晶圆加工去除量波动100-110nm（要求≤60nm）'
处理单元格 [7,2]: '${D2问题如何发生}'
    找到匹配: ${D2问题如何发生} -> 抛头加压过程中弹簧板翘曲、下抛头GAP值及重量不一致导致压力波动
    替换结果: '${D2问题如何发生}' -> '抛头加压过程中弹簧板翘曲、下抛头GAP值及重量不一致导致压力波动'
处理单元格 [8,2]: '${D2问题影响程度}'
    找到匹配: ${D2问题影响程度} -> 影响晶圆加工质量，严重程度一般，发生1次
    替换结果: '${D2问题影响程度}' -> '影响晶圆加工质量，严重程度一般，发生1次'
表格 7 处理完成，有替换
处理表格 8:
表格大小: 8行 x 2列
表格 8 处理完成，无替换
处理表格 9:
表格大小: 8行 x 2列
表格 9 处理完成，无替换
处理表格 10:
表格大小: 8行 x 2列
表格 10 处理完成，无替换
处理表格 11:
表格大小: 8行 x 2列
表格 11 处理完成，无替换
处理表格 12:
表格大小: 5行 x 7列
处理单元格 [2,2]: '${D3范围1}'
    找到匹配: ${D3范围1} -> 在制（客户端）
    替换结果: '${D3范围1}' -> '在制（客户端）'
处理单元格 [2,3]: '${D3处置对策1}'
    找到匹配: ${D3处置对策1} -> 调整GAP值使抛头压力一致
    替换结果: '${D3处置对策1}' -> '调整GAP值使抛头压力一致'
处理单元格 [2,4]: '${D3责任人1}'
    找到匹配: ${D3责任人1} -> 彭志远
    替换结果: '${D3责任人1}' -> '彭志远'
处理单元格 [2,5]: '${D3完成期限1}'
    找到匹配: ${D3完成期限1} -> 2024-04-01
    替换结果: '${D3完成期限1}' -> '2024-04-01'
处理单元格 [2,6]: '${D3状态1}'
    找到匹配: ${D3状态1} -> 执行完成
    替换结果: '${D3状态1}' -> '执行完成'
处理单元格 [2,7]: '${D3进度备注1}'
    找到匹配: ${D3进度备注1} -> 去除量波动达标
    替换结果: '${D3进度备注1}' -> '去除量波动达标'
处理单元格 [3,2]: '${D3范围2}'
    找到匹配: ${D3范围2} -> 在途（同型号机台）
    替换结果: '${D3范围2}' -> '在途（同型号机台）'
处理单元格 [3,3]: '${D3处置对策2}'
    找到匹配: ${D3处置对策2} -> 横向排查压力一致性
    替换结果: '${D3处置对策2}' -> '横向排查压力一致性'
处理单元格 [3,4]: '${D3责任人2}'
    找到匹配: ${D3责任人2} -> 马镇泽,历炜栋,王宝林
    替换结果: '${D3责任人2}' -> '马镇泽,历炜栋,王宝林'
处理单元格 [3,5]: '${D3完成期限2}'
    找到匹配: ${D3完成期限2} -> 2024-04-01
    替换结果: '${D3完成期限2}' -> '2024-04-01'
处理单元格 [3,6]: '${D3状态2}'
    找到匹配: ${D3状态2} -> 执行中
    替换结果: '${D3状态2}' -> '执行中'
处理单元格 [3,7]: '${D3进度备注2}'
    找到匹配: ${D3进度备注2} -> 100%机台存在压力波动异常
    替换结果: '${D3进度备注2}' -> '100%机台存在压力波动异常'
处理单元格 [4,2]: '${D3范围3}'
    删除未匹配的占位符: ['${D3范围3}']
    替换结果: '${D3范围3}' -> ''
处理单元格 [4,3]: '${D3处置对策3}'
    删除未匹配的占位符: ['${D3处置对策3}']
    替换结果: '${D3处置对策3}' -> ''
处理单元格 [4,4]: '${D3责任人3}'
    删除未匹配的占位符: ['${D3责任人3}']
    替换结果: '${D3责任人3}' -> ''
处理单元格 [4,5]: '${D3完成期限3}'
    删除未匹配的占位符: ['${D3完成期限3}']
    替换结果: '${D3完成期限3}' -> ''
处理单元格 [4,6]: '${D3状态3}'
    删除未匹配的占位符: ['${D3状态3}']
    替换结果: '${D3状态3}' -> ''
处理单元格 [4,7]: '${D3进度备注3}'
    删除未匹配的占位符: ['${D3进度备注3}']
    替换结果: '${D3进度备注3}' -> ''
处理单元格 [5,2]: '${D3范围4}'
    删除未匹配的占位符: ['${D3范围4}']
    替换结果: '${D3范围4}' -> ''
处理单元格 [5,3]: '${D3处置对策4}'
    删除未匹配的占位符: ['${D3处置对策4}']
    替换结果: '${D3处置对策4}' -> ''
处理单元格 [5,4]: '${D3责任人4}'
    删除未匹配的占位符: ['${D3责任人4}']
    替换结果: '${D3责任人4}' -> ''
处理单元格 [5,5]: '${D3完成期限4}'
    删除未匹配的占位符: ['${D3完成期限4}']
    替换结果: '${D3完成期限4}' -> ''
处理单元格 [5,6]: '${D3状态4}'
    删除未匹配的占位符: ['${D3状态4}']
    替换结果: '${D3状态4}' -> ''
处理单元格 [5,7]: '${D3进度备注4}'
    删除未匹配的占位符: ['${D3进度备注4}']
    替换结果: '${D3进度备注4}' -> ''
表格 12 处理完成，有替换
处理表格 13:
表格大小: 5行 x 7列
表格 13 处理完成，无替换
处理表格 14:
表格大小: 5行 x 7列
表格 14 处理完成，无替换
处理表格 15:
表格大小: 5行 x 7列
表格 15 处理完成，无替换
处理表格 16:
表格大小: 5行 x 7列
表格 16 处理完成，无替换
处理表格 17:
表格大小: 6行 x 3列
处理单元格 [2,2]: '${D4why1}'
    找到匹配: ${D4why1} -> 为什么去除量波动大？
    替换结果: '${D4why1}' -> '为什么去除量波动大？'
处理单元格 [2,3]: '${D4answer1}'
    找到匹配: ${D4answer1} -> 抛头压力波动异常
    替换结果: '${D4answer1}' -> '抛头压力波动异常'
处理单元格 [3,2]: '${D4why2}'
    找到匹配: ${D4why2} -> 为什么压力波动异常？
    替换结果: '${D4why2}' -> '为什么压力波动异常？'
处理单元格 [3,3]: '${D4answer2}'
    找到匹配: ${D4answer2} -> 弹簧板翘曲及下抛头GAP/重量不一致
    替换结果: '${D4answer2}' -> '弹簧板翘曲及下抛头GAP/重量不一致'
处理单元格 [4,2]: '${D4why3}'
    找到匹配: ${D4why3} -> 为什么GAP/重量不一致？
    替换结果: '${D4why3}' -> '为什么GAP/重量不一致？'
处理单元格 [4,3]: '${D4answer3}'
    找到匹配: ${D4answer3} -> 抛头组件公差未约束
    替换结果: '${D4answer3}' -> '抛头组件公差未约束'
处理单元格 [5,2]: '${D4why4}'
    找到匹配: ${D4why4} -> 为什么公差未约束？
    替换结果: '${D4why4}' -> '为什么公差未约束？'
处理单元格 [5,3]: '${D4answer4}'
    找到匹配: ${D4answer4} -> 设计图纸未规定平面度及重量公差
    替换结果: '${D4answer4}' -> '设计图纸未规定平面度及重量公差'
处理单元格 [6,2]: '${D4why5}'
    找到匹配: ${D4why5} -> 为什么未规定？
    替换结果: '${D4why5}' -> '为什么未规定？'
处理单元格 [6,3]: '${D4answer5}'
    找到匹配: ${D4answer5} -> 未识别加工去除量对公差的敏感性
    替换结果: '${D4answer5}' -> '未识别加工去除量对公差的敏感性'
表格 17 处理完成，有替换
处理表格 18:
表格大小: 8行 x 4列
处理单元格 [2,2]: '${D4可能原因1}'
    找到匹配: ${D4可能原因1} -> 弹簧板平面度不良
    替换结果: '${D4可能原因1}' -> '弹簧板平面度不良'
处理单元格 [2,3]: '${D4判定1}'
    找到匹配: ${D4判定1} -> 是
    替换结果: '${D4判定1}' -> '是'
处理单元格 [2,4]: '${D4证据1}'
    找到匹配: ${D4证据1} -> 压力测试显示波动异常，零件检测数据超标
    替换结果: '${D4证据1}' -> '压力测试显示波动异常，零件检测数据超标'
处理单元格 [3,2]: '${D4可能原因2}'
    找到匹配: ${D4可能原因2} -> 下抛头GAP/重量公差过大
    替换结果: '${D4可能原因2}' -> '下抛头GAP/重量公差过大'
处理单元格 [3,3]: '${D4判定2}'
    找到匹配: ${D4判定2} -> 是
    替换结果: '${D4判定2}' -> '是'
处理单元格 [3,4]: '${D4证据2}'
    找到匹配: ${D4证据2} -> 压力测试显示波动异常，零件检测数据超标
    替换结果: '${D4证据2}' -> '压力测试显示波动异常，零件检测数据超标'
处理单元格 [4,2]: '${D4可能原因3}'
    找到匹配: ${D4可能原因3} -> 
172.18.0.6 - - [26/Jun/2025 19:20:54] "POST /process_ppt HTTP/1.1" 200 -
    替换结果: '${D4可能原因3}' -> ''
处理单元格 [4,3]: '${D4判定3}'
    找到匹配: ${D4判定3} -> 
    替换结果: '${D4判定3}' -> ''
处理单元格 [4,4]: '${D4证据3}'
    找到匹配: ${D4证据3} -> 
    替换结果: '${D4证据3}' -> ''
处理单元格 [5,2]: '${D4可能原因4}'
    删除未匹配的占位符: ['${D4可能原因4}']
    替换结果: '${D4可能原因4}' -> ''
处理单元格 [5,3]: '${D4判定4}'
    删除未匹配的占位符: ['${D4判定4}']
    替换结果: '${D4判定4}' -> ''
处理单元格 [5,4]: '${D4证据4}'
    删除未匹配的占位符: ['${D4证据4}']
    替换结果: '${D4证据4}' -> ''
处理单元格 [6,2]: '${D4可能原因5}'
    删除未匹配的占位符: ['${D4可能原因5}']
    替换结果: '${D4可能原因5}' -> ''
处理单元格 [6,3]: '${D4判定5}'
    删除未匹配的占位符: ['${D4判定5}']
    替换结果: '${D4判定5}' -> ''
处理单元格 [6,4]: '${D4证据5}'
    删除未匹配的占位符: ['${D4证据5}']
    替换结果: '${D4证据5}' -> ''
处理单元格 [7,2]: '${D4可能原因6}'
    删除未匹配的占位符: ['${D4可能原因6}']
    替换结果: '${D4可能原因6}' -> ''
处理单元格 [7,3]: '${D4判定6}'
    删除未匹配的占位符: ['${D4判定6}']
    替换结果: '${D4判定6}' -> ''
处理单元格 [7,4]: '${D4证据6}'
    删除未匹配的占位符: ['${D4证据6}']
    替换结果: '${D4证据6}' -> ''
处理单元格 [8,2]: '${D4可能原因7}'
    删除未匹配的占位符: ['${D4可能原因7}']
    替换结果: '${D4可能原因7}' -> ''
处理单元格 [8,3]: '${D4判定7}'
    删除未匹配的占位符: ['${D4判定7}']
    替换结果: '${D4判定7}' -> ''
处理单元格 [8,4]: '${D4证据7}'
    删除未匹配的占位符: ['${D4证据7}']
    替换结果: '${D4证据7}' -> ''
表格 18 处理完成，有替换
处理表格 19:
表格大小: 6行 x 3列
表格 19 处理完成，无替换
处理表格 20:
表格大小: 8行 x 4列
表格 20 处理完成，无替换
处理表格 21:
表格大小: 6行 x 3列
表格 21 处理完成，无替换
处理表格 22:
表格大小: 8行 x 4列
表格 22 处理完成，无替换
处理表格 23:
表格大小: 6行 x 3列
表格 23 处理完成，无替换
处理表格 24:
表格大小: 8行 x 4列
表格 24 处理完成，无替换
处理表格 25:
表格大小: 6行 x 3列
表格 25 处理完成，无替换
处理表格 26:
表格大小: 8行 x 4列
表格 26 处理完成，无替换
处理表格 27:
表格大小: 5行 x 4列
处理单元格 [2,2]: '${D5纠正措施1}'
    找到匹配: ${D5纠正措施1} -> 修订弹簧板平面度要求至0.1mm，约束抛头固定环/浮动基座尺寸重量公差
    替换结果: '${D5纠正措施1}' -> '修订弹簧板平面度要求至0.1mm，约束抛头固定环/浮动基座尺寸重量公差'
处理单元格 [2,3]: '${D5责任人1}'
    找到匹配: ${D5责任人1} -> 黄金涛,马镇泽,莫易栋,厉炜栋
    替换结果: '${D5责任人1}' -> '黄金涛,马镇泽,莫易栋,厉炜栋'
处理单元格 [2,4]: '${D5计划完成日期1}'
    找到匹配: ${D5计划完成日期1} -> 2024-04-02至2024-05-30
    替换结果: '${D5计划完成日期1}' -> '2024-04-02至2024-05-30'
处理单元格 [3,2]: '${D5纠正措施2}'
    删除未匹配的占位符: ['${D5纠正措施2}']
    替换结果: '${D5纠正措施2}' -> ''
处理单元格 [3,3]: '${D5责任人2}'
    删除未匹配的占位符: ['${D5责任人2}']
    替换结果: '${D5责任人2}' -> ''
处理单元格 [3,4]: '${D5计划完成日期2}'
    删除未匹配的占位符: ['${D5计划完成日期2}']
    替换结果: '${D5计划完成日期2}' -> ''
处理单元格 [4,2]: '${D5纠正措施3}'
    删除未匹配的占位符: ['${D5纠正措施3}']
    替换结果: '${D5纠正措施3}' -> ''
处理单元格 [4,3]: '${D5责任人3}'
    删除未匹配的占位符: ['${D5责任人3}']
    替换结果: '${D5责任人3}' -> ''
处理单元格 [4,4]: '${D5计划完成日期3}'
    删除未匹配的占位符: ['${D5计划完成日期3}']
    替换结果: '${D5计划完成日期3}' -> ''
处理单元格 [5,2]: '${D5纠正措施4}'
    删除未匹配的占位符: ['${D5纠正措施4}']
    替换结果: '${D5纠正措施4}' -> ''
处理单元格 [5,3]: '${D5责任人4}'
    删除未匹配的占位符: ['${D5责任人4}']
    替换结果: '${D5责任人4}' -> ''
处理单元格 [5,4]: '${D5计划完成日期4}'
    删除未匹配的占位符: ['${D5计划完成日期4}']
    替换结果: '${D5计划完成日期4}' -> ''
表格 27 处理完成，有替换
处理表格 28:
表格大小: 5行 x 4列
表格 28 处理完成，无替换
处理表格 29:
表格大小: 5行 x 4列
表格 29 处理完成，无替换
处理表格 30:
表格大小: 5行 x 4列
表格 30 处理完成，无替换
处理表格 31:
表格大小: 5行 x 4列
表格 31 处理完成，无替换
处理表格 32:
表格大小: 5行 x 5列
处理单元格 [2,2]: '${D6措施验证1}'
    找到匹配: ${D6措施验证1} -> 更换平面度0.1mm弹簧板及公差约束组件并测试
    替换结果: '${D6措施验证1}' -> '更换平面度0.1mm弹簧板及公差约束组件并测试'
处理单元格 [2,3]: '${D6验证人1}'
    找到匹配: ${D6验证人1} -> 厉炜栋,彭志远,祝康康
    替换结果: '${D6验证人1}' -> '厉炜栋,彭志远,祝康康'
处理单元格 [2,4]: '${D6验证时间1}'
    找到匹配: ${D6验证时间1} -> 2024-05-30
    替换结果: '${D6验证时间1}' -> '2024-05-30'
处理单元格 [2,5]: '${D6验证结果1}'
    找到匹配: ${D6验证结果1} -> 去除量波动≤60nm，压力波动≤±1.2N
    替换结果: '${D6验证结果1}' -> '去除量波动≤60nm，压力波动≤±1.2N'
处理单元格 [3,2]: '${D6措施验证2}'
    删除未匹配的占位符: ['${D6措施验证2}']
    替换结果: '${D6措施验证2}' -> ''
处理单元格 [3,3]: '${D6验证人2}'
    删除未匹配的占位符: ['${D6验证人2}']
    替换结果: '${D6验证人2}' -> ''
处理单元格 [3,4]: '${D6验证时间2}'
    删除未匹配的占位符: ['${D6验证时间2}']
    替换结果: '${D6验证时间2}' -> ''
处理单元格 [3,5]: '${D6验证结果2}'
    删除未匹配的占位符: ['${D6验证结果2}']
    替换结果: '${D6验证结果2}' -> ''
处理单元格 [4,2]: '${D6措施验证3}'
    删除未匹配的占位符: ['${D6措施验证3}']
    替换结果: '${D6措施验证3}' -> ''
处理单元格 [4,3]: '${D6验证人3}'
    删除未匹配的占位符: ['${D6验证人3}']
    替换结果: '${D6验证人3}' -> ''
处理单元格 [4,4]: '${D6验证时间3}'
    删除未匹配的占位符: ['${D6验证时间3}']
    替换结果: '${D6验证时间3}' -> ''
处理单元格 [4,5]: '${D6验证结果3}'
    删除未匹配的占位符: ['${D6验证结果3}']
    替换结果: '${D6验证结果3}' -> ''
处理单元格 [5,2]: '${D6措施验证4}'
    删除未匹配的占位符: ['${D6措施验证4}']
    替换结果: '${D6措施验证4}' -> ''
处理单元格 [5,3]: '${D6验证人4}'
    删除未匹配的占位符: ['${D6验证人4}']
    替换结果: '${D6验证人4}' -> ''
处理单元格 [5,4]: '${D6验证时间4}'
    删除未匹配的占位符: ['${D6验证时间4}']
    替换结果: '${D6验证时间4}' -> ''
处理单元格 [5,5]: '${D6验证结果4}'
    删除未匹配的占位符: ['${D6验证结果4}']
    替换结果: '${D6验证结果4}' -> ''
表格 32 处理完成，有替换
处理表格 33:
表格大小: 5行 x 5列
表格 33 处理完成，无替换
处理表格 34:
表格大小: 5行 x 5列
表格 34 处理完成，无替换
处理表格 35:
表格大小: 5行 x 5列
表格 35 处理完成，无替换
处理表格 36:
表格大小: 5行 x 5列
表格 36 处理完成，无替换
处理表格 37:
表格大小: 5行 x 4列
处理单元格 [2,2]: '${D7预防措施1}'
    找到匹配: ${D7预防措施1} -> 修订弹簧板图纸增加平面度要求，更新抛头组件公差标准至SOP
    替换结果: '${D7预防措施1}' -> '修订弹簧板图纸增加平面度要求，更新抛头组件公差标准至SOP'
172.18.0.6 - - [26/Jun/2025 19:20:54] "POST /process_docx HTTP/1.1" 200 -
10.60.13.200 - - [26/Jun/2025 19:21:02] "GET /download/60d32531-ad18-44e1-bd18-41c8754ef24e HTTP/1.1" 200 -
10.60.13.200 - - [26/Jun/2025 19:21:02] "GET /favicon.ico HTTP/1.1" 404 -
10.60.13.200 - - [26/Jun/2025 19:21:05] "GET /download_file/60d32531-ad18-44e1-bd18-41c8754ef24e HTTP/1.1" 200 -
10.60.13.200 - - [26/Jun/2025 19:22:28] "GET /download/82a6f026-9852-4fa9-9bb9-42b1f5fa06a7 HTTP/1.1" 200 -
10.60.13.200 - - [26/Jun/2025 19:22:30] "GET /download_file/82a6f026-9852-4fa9-9bb9-42b1f5fa06a7 HTTP/1.1" 200 -
