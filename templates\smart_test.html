<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能PPT/DOCX处理测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h2 {
            color: #34495e;
            margin-top: 0;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin: 15px 0;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
        }
        .btn-success {
            background-color: #27ae60;
            color: white;
        }
        .btn-success:hover {
            background-color: #229954;
        }
        .btn-warning {
            background-color: #f39c12;
            color: white;
        }
        .btn-warning:hover {
            background-color: #e67e22;
        }
        .result-area {
            margin-top: 20px;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 5px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .loading {
            color: #3498db;
            font-style: italic;
        }
        .success {
            color: #27ae60;
        }
        .error {
            color: #e74c3c;
        }
        .info {
            background-color: #d5dbdb;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .download-link {
            display: inline-block;
            margin: 10px 10px 10px 0;
            padding: 8px 16px;
            background-color: #2ecc71;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .download-link:hover {
            background-color: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 智能PPT/DOCX处理测试平台</h1>
        
        <div class="info">
            <strong>功能说明：</strong>
            <ul>
                <li>🧠 <strong>智能分析</strong>：自动分析数据结构，识别空白内容和溢出内容</li>
                <li>🎨 <strong>空白清理</strong>：自动隐藏空白sections，清理空白表格行</li>
                <li>📄 <strong>自动分页</strong>：内容溢出时自动创建额外页面</li>
                <li>⚡ <strong>零配置</strong>：无需修改模板，即可享受智能功能</li>
            </ul>
        </div>

        <!-- 数据分析测试 -->
        <div class="test-section">
            <h2>📊 数据结构分析测试</h2>
            <p>测试智能数据分析功能，检测空白内容和溢出内容</p>
            <div class="button-group">
                <button class="btn-primary" onclick="testDataAnalysis('minimal')">测试最小数据（空白多）</button>
                <button class="btn-warning" onclick="testDataAnalysis('overflow')">测试溢出数据（内容多）</button>
            </div>
            <div id="analysis-result" class="result-area">点击按钮开始测试...</div>
        </div>

        <!-- 智能PPT生成测试 -->
        <div class="test-section">
            <h2>📋 智能PPT生成测试</h2>
            <p>使用智能功能生成PPT，自动处理空白内容和溢出内容</p>
            <div class="button-group">
                <button class="btn-success" onclick="generateSmartPPT('minimal')">生成简洁PPT（最小数据）</button>
                <button class="btn-success" onclick="generateSmartPPT('overflow')">生成完整PPT（溢出数据）</button>
            </div>
            <div id="ppt-result" class="result-area">点击按钮开始生成...</div>
        </div>

        <!-- 智能DOCX生成测试 -->
        <div class="test-section">
            <h2>📄 智能DOCX生成测试</h2>
            <p>使用智能功能生成DOCX，自动处理空白内容和溢出内容</p>
            <div class="button-group">
                <button class="btn-success" onclick="generateSmartDOCX('minimal')">生成简洁DOCX（最小数据）</button>
                <button class="btn-success" onclick="generateSmartDOCX('overflow')">生成完整DOCX（溢出数据）</button>
            </div>
            <div id="docx-result" class="result-area">点击按钮开始生成...</div>
        </div>
    </div>

    <script>
        // 测试数据定义
        const testData = {
            minimal: {
                "D0汇报信息": {
                    "${D0标题}": "设备故障问题",
                    "${D0汇报人}": "张工程师",
                    "${D0汇报时间}": "2025-01-23",
                    "${D0项目背景}": "生产线设备故障"
                },
                "D1建立小组": {
                    "组长": {
                        "${D1组长姓名}": "李经理",
                        "${D1组长部门}": "生产部",
                        "${D1组长职位}": "生产经理",
                        "${D1组长主要职责}": "负责问题解决"
                    }
                },
                "D2问题描述": {
                    "${D2事件整体描述}": "设备突然停机",
                    "5W2H": {
                        "${D2何时发生}": "2025-01-23 10:00",
                        "${D2何地发生}": "生产线A区",
                        "${D2何人发现}": "操作员",
                        "${D2为什么是这问题}": "设备老化",
                        "${D2发生了什么问题}": "主轴承损坏",
                        "${D2问题如何发生}": "缺乏维护",
                        "${D2问题影响程度}": "严重"
                    }
                },
                "D3临时措施": {},
                "D4根本原因": {
                    "${D4原因小结}": "设备维护不到位"
                },
                "D5永久措施": {},
                "D6措施验证": {},
                "D7预防措施": {},
                "D8庆贺团队": {
                    "${D8有效性确认}": "问题已解决",
                    "${D8确认人}": "李经理",
                    "${D8确认完成时间}": "2025-01-23"
                }
            },
            overflow: {
                "D0汇报信息": {
                    "${D0标题}": "供应链质量系统性问题",
                    "${D0汇报人}": "质量总监",
                    "${D0汇报时间}": "2025-01-23",
                    "${D0项目背景}": "多个供应商质量问题"
                },
                "D1建立小组": {
                    "组长": {"${D1组长姓名}": "王总监", "${D1组长部门}": "质量部", "${D1组长职位}": "总监", "${D1组长主要职责}": "统筹"},
                    "成员1": {"${D1成员1姓名}": "张经理", "${D1成员1部门}": "采购部", "${D1成员1职位}": "经理", "${D1成员1主要职责}": "供应商管理"},
                    "成员2": {"${D1成员2姓名}": "李工程师", "${D1成员2部门}": "质量部", "${D1成员2职位}": "工程师", "${D1成员2主要职责}": "质量分析"},
                    "成员3": {"${D1成员3姓名}": "赵主管", "${D1成员3部门}": "生产部", "${D1成员3职位}": "主管", "${D1成员3主要职责}": "生产控制"},
                    "成员4": {"${D1成员4姓名}": "钱分析师", "${D1成员4部门}": "财务部", "${D1成员4职位}": "分析师", "${D1成员4主要职责}": "成本分析"},
                    "成员5": {"${D1成员5姓名}": "孙经理", "${D1成员5部门}": "销售部", "${D1成员5职位}": "经理", "${D1成员5主要职责}": "客户沟通"},
                    "成员6": {"${D1成员6姓名}": "周工程师", "${D1成员6部门}": "研发部", "${D1成员6职位}": "工程师", "${D1成员6主要职责}": "技术支持"},
                    "成员7": {"${D1成员7姓名}": "吴主管", "${D1成员7部门}": "物流部", "${D1成员7职位}": "主管", "${D1成员7主要职责}": "物流协调"},
                    "成员8": {"${D1成员8姓名}": "郑专员", "${D1成员8部门}": "法务部", "${D1成员8职位}": "专员", "${D1成员8主要职责}": "法律风险"}
                },
                "D3临时措施": {
                    "临时措施1": {"${D3范围1}": "供应商A", "${D3处置对策1}": "暂停供货", "${D3责任人1}": "张经理", "${D3完成期限1}": "2025-01-25", "${D3状态1}": "进行中", "${D3进度备注1}": "已通知"},
                    "临时措施2": {"${D3范围2}": "供应商B", "${D3处置对策2}": "全检", "${D3责任人2}": "李工程师", "${D3完成期限2}": "2025-01-24", "${D3状态2}": "完成", "${D3进度备注2}": "已实施"},
                    "临时措施3": {"${D3范围3}": "供应商C", "${D3处置对策3}": "现场指导", "${D3责任人3}": "李工程师", "${D3完成期限3}": "2025-01-26", "${D3状态3}": "计划中", "${D3进度备注3}": "安排中"},
                    "临时措施4": {"${D3范围4}": "内部生产", "${D3处置对策4}": "启动备用", "${D3责任人4}": "赵主管", "${D3完成期限4}": "2025-01-24", "${D3状态4}": "完成", "${D3进度备注4}": "已激活"},
                    "临时措施5": {"${D3范围5}": "客户沟通", "${D3处置对策5}": "主动通知", "${D3责任人5}": "孙经理", "${D3完成期限5}": "2025-01-25", "${D3状态5}": "进行中", "${D3进度备注5}": "已联系"},
                    "临时措施6": {"${D3范围6}": "库存管理", "${D3处置对策6}": "隔离问题批次", "${D3责任人6}": "吴主管", "${D3完成期限6}": "2025-01-24", "${D3状态6}": "完成", "${D3进度备注6}": "已隔离"},
                    "临时措施7": {"${D3范围7}": "质量体系", "${D3处置对策7}": "修订协议", "${D3责任人7}": "郑专员", "${D3完成期限7}": "2025-01-27", "${D3状态7}": "进行中", "${D3进度备注7}": "起草中"}
                },
                "D5永久措施": {
                    "措施1": {"${D5纠正措施1}": "建立供应商分级管理", "${D5责任人1}": "张经理", "${D5计划完成日期1}": "2025-03-01"},
                    "措施2": {"${D5纠正措施2}": "质量能力评估", "${D5责任人2}": "李工程师", "${D5计划完成日期2}": "2025-02-15"},
                    "措施3": {"${D5纠正措施3}": "数据平台建设", "${D5责任人3}": "周工程师", "${D5计划完成日期3}": "2025-04-01"},
                    "措施4": {"${D5纠正措施4}": "检验流程完善", "${D5责任人4}": "李工程师", "${D5计划完成日期4}": "2025-02-20"},
                    "措施5": {"${D5纠正措施5}": "快速响应机制", "${D5责任人5}": "张经理", "${D5计划完成日期5}": "2025-02-10"},
                    "措施6": {"${D5纠正措施6}": "现场审核制度", "${D5责任人6}": "王总监", "${D5计划完成日期6}": "2025-03-15"},
                    "措施7": {"${D5纠正措施7}": "培训体系建立", "${D5责任人7}": "李工程师", "${D5计划完成日期7}": "2025-03-30"}
                }
            }
        };

        // 测试数据分析
        async function testDataAnalysis(dataType) {
            const resultDiv = document.getElementById('analysis-result');
            resultDiv.innerHTML = '<span class="loading">🔄 正在分析数据结构...</span>';
            
            try {
                const response = await fetch('/test_smart_processing', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData[dataType])
                });
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    const analysis = result.analysis;
                    let output = `✅ 数据分析完成 (${dataType === 'minimal' ? '最小数据' : '溢出数据'})\n\n`;
                    output += `📊 分析结果:\n`;
                    output += `- 有内容的sections: ${analysis.recommendations.empty_sections.length > 0 ? '部分' : '全部'}\n`;
                    output += `- 空白的sections: ${analysis.recommendations.empty_sections.join(', ') || '无'}\n`;
                    output += `- 溢出的sections: ${analysis.recommendations.overflow_sections.join(', ') || '无'}\n`;
                    output += `- 总溢出项数: ${analysis.recommendations.total_overflow_items}\n`;
                    output += `- 建议额外页面: ${analysis.recommendations.suggested_additional_pages}\n\n`;
                    output += `🎯 智能处理建议:\n`;
                    if (analysis.recommendations.empty_sections.length > 0) {
                        output += `- 自动隐藏 ${analysis.recommendations.empty_sections.length} 个空白sections\n`;
                    }
                    if (analysis.recommendations.suggested_additional_pages > 0) {
                        output += `- 创建 ${analysis.recommendations.suggested_additional_pages} 个额外页面\n`;
                    }
                    if (analysis.recommendations.empty_sections.length === 0 && analysis.recommendations.suggested_additional_pages === 0) {
                        output += `- 数据结构合理，无需特殊处理\n`;
                    }
                    
                    resultDiv.innerHTML = `<span class="success">${output}</span>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 分析失败: ${result.error}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 请求失败: ${error.message}</span>`;
            }
        }

        // 生成智能PPT
        async function generateSmartPPT(dataType) {
            const resultDiv = document.getElementById('ppt-result');
            resultDiv.innerHTML = '<span class="loading">🔄 正在生成智能PPT...</span>';
            
            try {
                const response = await fetch('/process_smart_ppt', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        template_name: '8d_template/0610/template.pptx',
                        replacements: testData[dataType]
                    })
                });
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    let output = `✅ 智能PPT生成成功 (${dataType === 'minimal' ? '最小数据' : '溢出数据'})\n\n`;
                    output += `📁 文件名: ${result.filename}\n`;
                    output += `🔗 下载链接: `;
                    
                    resultDiv.innerHTML = `<span class="success">${output}</span>`;
                    resultDiv.innerHTML += `<a href="${result.download_url}" class="download-link" target="_blank">📥 下载PPT文件</a>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 生成失败: ${result.error}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 请求失败: ${error.message}</span>`;
            }
        }

        // 生成智能DOCX
        async function generateSmartDOCX(dataType) {
            const resultDiv = document.getElementById('docx-result');
            resultDiv.innerHTML = '<span class="loading">🔄 正在生成智能DOCX...</span>';
            
            try {
                const response = await fetch('/process_smart_docx', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        template_name: '8d_template/0610/template.docx',
                        replacements: testData[dataType]
                    })
                });
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    let output = `✅ 智能DOCX生成成功 (${dataType === 'minimal' ? '最小数据' : '溢出数据'})\n\n`;
                    output += `📁 文件名: ${result.filename}\n`;
                    output += `🔗 下载链接: `;
                    
                    resultDiv.innerHTML = `<span class="success">${output}</span>`;
                    resultDiv.innerHTML += `<a href="${result.download_url}" class="download-link" target="_blank">📥 下载DOCX文件</a>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 生成失败: ${result.error}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 请求失败: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
