body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.download-container {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    text-align: center;
    max-width: 500px;
    width: 90%;
}

.download-icon {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 20px;
}

.download-title {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.download-subtitle {
    color: #666;
    margin-bottom: 30px;
    font-size: 1rem;
}

.file-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    border-left: 4px solid #667eea;
}

.file-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.file-type {
    color: #667eea;
    text-transform: uppercase;
    font-size: 0.9rem;
    font-weight: 500;
}

.download-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 20px;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.progress-container {
    display: none;
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    color: #666;
    font-size: 0.9rem;
}

.error-message {
    color: #e74c3c;
    background: #fdf2f2;
    border: 1px solid #fadada;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.success-message {
    color: #27ae60;
    background: #f1f9f4;
    border: 1px solid #d4e6da;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
} 