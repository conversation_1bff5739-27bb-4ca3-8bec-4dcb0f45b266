<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8D报告协作编辑</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/report_preview.css') }}">
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>8D报告协作编辑</h1>
            <div class="header-actions">
                <button id="save-session-btn" class="btn btn-secondary">保存会话</button>
                <button id="back-to-form-btn" class="btn btn-outline" onclick="window.location.href='{{ url_for('index') }}'">返回表单</button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 左侧：报告内容预览 -->
            <div class="preview-panel">
                <div class="preview-header">
                    <h2>报告内容预览</h2>
                    <div class="preview-actions">
                        <button id="expand-all-btn" class="btn btn-small">展开全部</button>
                        <button id="collapse-all-btn" class="btn btn-small">收起全部</button>
                    </div>
                </div>
                
                <div class="preview-content" id="preview-content">
                    <!-- D0-D8 内容将通过JavaScript动态生成 -->
                    <div class="loading-indicator">
                        <div class="spinner"></div>
                        <p>正在加载报告内容...</p>
                    </div>
                </div>

                <!-- 底部操作按钮 -->
                <div class="preview-footer">
                    <button id="generate-documents-btn" class="btn btn-primary btn-large">
                        <span class="icon">📄</span>
                        生成最终文档
                    </button>
                    <div class="generation-status" id="generation-status" style="display: none;">
                        <div class="status-text">正在生成文档...</div>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：AI对话助手 -->
            <div class="chat-panel">
                <div class="chat-header">
                    <h2>AI对话助手</h2>
                    <div class="chat-status">
                        <span class="status-indicator" id="ai-status">●</span>
                        <span id="ai-status-text">就绪</span>
                    </div>
                </div>

                <!-- 对话历史 -->
                <div class="chat-history" id="chat-history">
                    <div class="welcome-message">
                        <div class="message ai-message">
                            <div class="message-content">
                                <p>👋 您好！我是您的8D报告AI助手。</p>
                                <p>您可以告诉我需要如何改进报告内容，比如：</p>
                                <ul>
                                    <li>"D4的根本原因分析不够深入，请补充设备因素"</li>
                                    <li>"D5的纠正措施太抽象，请提供具体步骤"</li>
                                    <li>"请完善D7的预防措施"</li>
                                </ul>
                                <p>我会实时更新左侧的报告内容。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速建议 -->
                <div class="quick-suggestions" id="quick-suggestions">
                    <div class="suggestions-title">快速建议：</div>
                    <div class="suggestion-buttons">
                        <button class="suggestion-btn" data-suggestion="请完善D4的根本原因分析，增加更多细节">完善根本原因</button>
                        <button class="suggestion-btn" data-suggestion="D5的纠正措施需要更具体，请提供详细的实施步骤">具体化纠正措施</button>
                        <button class="suggestion-btn" data-suggestion="请补充D7的预防措施，确保问题不再发生">补充预防措施</button>
                        <button class="suggestion-btn" data-suggestion="请检查整体内容的逻辑性和完整性">整体检查</button>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="chat-input-area">
                    <div class="input-container">
                        <textarea 
                            id="chat-input" 
                            placeholder="请输入您的修改意见..." 
                            rows="3"
                            maxlength="1000"
                        ></textarea>
                        <div class="input-actions">
                            <button id="send-btn" class="btn btn-primary">
                                <span class="icon">📤</span>
                                发送
                            </button>
                            <button id="voice-input-btn" class="btn btn-outline" title="语音输入">
                                <span class="icon">🎤</span>
                            </button>
                        </div>
                    </div>
                    <div class="input-footer">
                        <div class="char-count">
                            <span id="char-count">0</span>/1000
                        </div>
                        <div class="input-tips">
                            按 Ctrl+Enter 快速发送
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner-large"></div>
            <div class="loading-text">AI正在处理您的请求...</div>
        </div>
    </div>

    <!-- 文档生成成功模态框 -->
    <div class="modal" id="success-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🎉 文档生成成功！</h3>
            </div>
            <div class="modal-body">
                <p>您的8D报告已成功生成，请点击下方链接下载：</p>
                <div class="download-links" id="download-links">
                    <!-- 下载链接将通过JavaScript动态添加 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('success-modal')">关闭</button>
                <button class="btn btn-primary" onclick="window.location.href='{{ url_for('index') }}'">创建新报告</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 全局变量
        const SESSION_ID = '{{ session_id }}';
        let conversationId = null;
        let reportData = {{ report_data | tojson }};
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            bindEventListeners();
            renderReportPreview();
        });
    </script>
    <script src="{{ url_for('static', filename='js/report_preview.js') }}"></script>
</body>
</html> 