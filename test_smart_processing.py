#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能PPT/DOCX处理功能测试脚本
测试新的智能内容处理、空白清理、自动分页等功能
"""

import json
import os
import sys
from datetime import datetime
from pptx import Presentation
from docx import Document

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入应用模块
from app import (
    analyze_data_structure,
    create_smart_replacements,
    detect_content_overflow,
    optimize_content_distribution,
    process_ppt_with_smart_features,
    process_docx_with_smart_features
)


def create_test_data_minimal():
    """创建最小测试数据（模拟空白内容场景）"""
    return {
        "D0汇报信息": {
            "${D0标题}": "测试问题报告",
            "${D0汇报人}": "测试人员",
            "${D0汇报时间}": "2025-01-23",
            "${D0项目背景}": "这是一个测试场景"
        },
        "D1建立小组": {
            "组长": {
                "${D1组长姓名}": "张三",
                "${D1组长部门}": "测试部",
                "${D1组长职位}": "测试经理",
                "${D1组长主要职责}": "负责测试协调"
            }
            # 注意：没有成员数据，应该会清理空白行
        },
        "D2问题描述": {
            "${D2事件整体描述}": "测试问题描述",
            "5W2H": {
                "${D2何时发生}": "2025-01-23",
                "${D2何地发生}": "测试环境",
                "${D2何人发现}": "测试人员",
                "${D2为什么是这问题}": "测试原因",
                "${D2发生了什么问题}": "测试问题",
                "${D2问题如何发生}": "测试过程",
                "${D2问题影响程度}": "轻微"
            }
        },
        "D3临时措施": {
            # 完全空白的section
        },
        "D4根本原因": {
            "${D4原因小结}": "测试根本原因分析"
        },
        "D5永久措施": {
            # 完全空白的section
        },
        "D6措施验证": {
            # 完全空白的section
        },
        "D7预防措施": {
            # 完全空白的section
        },
        "D8庆贺团队": {
            "${D8有效性确认}": "测试确认",
            "${D8确认人}": "测试人员",
            "${D8确认完成时间}": "2025-01-23"
        }
    }


def create_test_data_overflow():
    """创建溢出测试数据（模拟内容过多场景）"""
    return {
        "D0汇报信息": {
            "${D0标题}": "复杂问题报告",
            "${D0汇报人}": "项目经理",
            "${D0汇报时间}": "2025-01-23",
            "${D0项目背景}": "这是一个复杂的问题场景，涉及多个方面"
        },
        "D1建立小组": {
            "组长": {
                "${D1组长姓名}": "张三",
                "${D1组长部门}": "质量部",
                "${D1组长职位}": "质量经理",
                "${D1组长主要职责}": "负责整体协调"
            },
            "成员1": {
                "${D1成员1姓名}": "李四",
                "${D1成员1部门}": "生产部",
                "${D1成员1职位}": "生产主管",
                "${D1成员1主要职责}": "生产过程控制"
            },
            "成员2": {
                "${D1成员2姓名}": "王五",
                "${D1成员2部门}": "工程部",
                "${D1成员2职位}": "工程师",
                "${D1成员2主要职责}": "技术分析"
            },
            "成员3": {
                "${D1成员3姓名}": "赵六",
                "${D1成员3部门}": "采购部",
                "${D1成员3职位}": "采购员",
                "${D1成员3主要职责}": "供应商管理"
            },
            "成员4": {
                "${D1成员4姓名}": "钱七",
                "${D1成员4部门}": "财务部",
                "${D1成员4职位}": "财务分析师",
                "${D1成员4主要职责}": "成本分析"
            },
            "成员5": {
                "${D1成员5姓名}": "孙八",
                "${D1成员5部门}": "销售部",
                "${D1成员5职位}": "销售经理",
                "${D1成员5主要职责}": "客户沟通"
            },
            "成员6": {
                "${D1成员6姓名}": "周九",
                "${D1成员6部门}": "研发部",
                "${D1成员6职位}": "研发工程师",
                "${D1成员6主要职责}": "产品改进"
            },
            "成员7": {
                "${D1成员7姓名}": "吴十",
                "${D1成员7部门}": "物流部",
                "${D1成员7职位}": "物流主管",
                "${D1成员7主要职责}": "物流协调"
            }
            # 8个成员，超过了建议的6个限制
        },
        "D2问题描述": {
            "${D2事件整体描述}": "复杂的多层次问题，涉及生产、质量、供应链等多个环节",
            "5W2H": {
                "${D2何时发生}": "2025-01-20 至 2025-01-23",
                "${D2何地发生}": "生产线A、B、C区域",
                "${D2何人发现}": "质量检验员、生产操作员、客户代表",
                "${D2为什么是这问题}": "多重因素导致的系统性问题",
                "${D2发生了什么问题}": "产品质量不稳定，客户投诉增加",
                "${D2问题如何发生}": "供应商材料变更 + 设备老化 + 人员培训不足",
                "${D2问题影响程度}": "严重，影响客户满意度和公司声誉"
            }
        },
        "D3临时措施": {
            "临时措施1": {
                "${D3范围1}": "生产线A区",
                "${D3处置对策1}": "立即停产检查，更换问题批次材料",
                "${D3责任人1}": "张三",
                "${D3完成期限1}": "2025-01-24",
                "${D3状态1}": "进行中",
                "${D3进度备注1}": "已完成50%"
            },
            "临时措施2": {
                "${D3范围2}": "生产线B区",
                "${D3处置对策2}": "加强质量检验频次，每小时检查一次",
                "${D3责任人2}": "李四",
                "${D3完成期限2}": "2025-01-25",
                "${D3状态2}": "已完成",
                "${D3进度备注2}": "检验频次已调整"
            },
            "临时措施3": {
                "${D3范围3}": "生产线C区",
                "${D3处置对策3}": "临时增加人工检验岗位",
                "${D3责任人3}": "王五",
                "${D3完成期限3}": "2025-01-26",
                "${D3状态3}": "计划中",
                "${D3进度备注3}": "正在安排人员"
            },
            "临时措施4": {
                "${D3范围4}": "供应商管理",
                "${D3处置对策4}": "要求供应商提供材料质量证明",
                "${D3责任人4}": "赵六",
                "${D3完成期限4}": "2025-01-27",
                "${D3状态4}": "进行中",
                "${D3进度备注4}": "已联系主要供应商"
            },
            "临时措施5": {
                "${D3范围5}": "客户沟通",
                "${D3处置对策5}": "主动联系受影响客户，说明情况",
                "${D3责任人5}": "孙八",
                "${D3完成期限5}": "2025-01-28",
                "${D3状态5}": "已完成",
                "${D3进度备注5}": "已联系所有受影响客户"
            },
            "临时措施6": {
                "${D3范围6}": "库存管理",
                "${D3处置对策6}": "隔离问题批次产品，防止流出",
                "${D3责任人6}": "吴十",
                "${D3完成期限6}": "2025-01-29",
                "${D3状态6}": "已完成",
                "${D3进度备注6}": "问题产品已全部隔离"
            }
            # 6个临时措施，超过了建议的5个限制
        },
        "D4根本原因": {
            "${D4原因小结}": "经过深入分析，发现问题根源在于供应商管理体系不完善、设备维护不及时、人员培训体系缺失等多个方面"
        },
        "D5永久措施": {
            "措施1": {
                "${D5纠正措施1}": "建立供应商质量管理体系，包括供应商审核、材料检验、质量协议等",
                "${D5责任人1}": "赵六",
                "${D5计划完成日期1}": "2025-03-01"
            },
            "措施2": {
                "${D5纠正措施2}": "制定设备预防性维护计划，定期检查和保养关键设备",
                "${D5责任人2}": "王五",
                "${D5计划完成日期2}": "2025-02-15"
            },
            "措施3": {
                "${D5纠正措施3}": "建立员工培训体系，包括技能培训、质量意识培训等",
                "${D5责任人3}": "张三",
                "${D5计划完成日期3}": "2025-02-28"
            },
            "措施4": {
                "${D5纠正措施4}": "完善质量管理流程，增加关键控制点",
                "${D5责任人4}": "李四",
                "${D5计划完成日期4}": "2025-02-20"
            },
            "措施5": {
                "${D5纠正措施5}": "建立客户反馈快速响应机制",
                "${D5责任人5}": "孙八",
                "${D5计划完成日期5}": "2025-02-10"
            },
            "措施6": {
                "${D5纠正措施6}": "实施库存管理系统升级，提高追溯能力",
                "${D5责任人6}": "吴十",
                "${D5计划完成日期6}": "2025-03-15"
            }
            # 6个永久措施，超过了建议的5个限制
        },
        "D6措施验证": {
            "验证1": {
                "${D6措施验证1}": "供应商管理体系验证：审核3家主要供应商",
                "${D6验证人1}": "赵六",
                "${D6验证时间1}": "2025-03-05",
                "${D6验证结果1}": "待验证"
            },
            "验证2": {
                "${D6措施验证2}": "设备维护计划验证：检查维护记录完整性",
                "${D6验证人2}": "王五",
                "${D6验证时间2}": "2025-02-20",
                "${D6验证结果2}": "待验证"
            },
            "验证3": {
                "${D6措施验证3}": "员工培训效果验证：技能考核通过率",
                "${D6验证人3}": "张三",
                "${D6验证时间3}": "2025-03-01",
                "${D6验证结果3}": "待验证"
            },
            "验证4": {
                "${D6措施验证4}": "质量流程验证：抽查生产记录",
                "${D6验证人4}": "李四",
                "${D6验证时间4}": "2025-02-25",
                "${D6验证结果4}": "待验证"
            },
            "验证5": {
                "${D6措施验证5}": "客户反馈机制验证：响应时间测试",
                "${D6验证人5}": "孙八",
                "${D6验证时间5}": "2025-02-15",
                "${D6验证结果5}": "待验证"
            },
            "验证6": {
                "${D6措施验证6}": "库存系统验证：追溯功能测试",
                "${D6验证人6}": "吴十",
                "${D6验证时间6}": "2025-03-20",
                "${D6验证结果6}": "待验证"
            }
            # 6个验证，超过了建议的5个限制
        },
        "D7预防措施": {
            "预防1": {
                "${D7预防措施1}": "建立跨部门质量委员会，定期评估质量风险",
                "${D7责任人1}": "张三",
                "${D7计划完成日期1}": "2025-04-01"
            },
            "预防2": {
                "${D7预防措施2}": "实施质量管理体系认证，提升管理水平",
                "${D7责任人2}": "李四",
                "${D7计划完成日期2}": "2025-06-01"
            },
            "预防3": {
                "${D7预防措施3}": "建立供应商长期合作伙伴关系",
                "${D7责任人3}": "赵六",
                "${D7计划完成日期3}": "2025-05-01"
            },
            "预防4": {
                "${D7预防措施4}": "实施设备智能化改造，提高自动化水平",
                "${D7责任人4}": "王五",
                "${D7计划完成日期4}": "2025-08-01"
            },
            "预防5": {
                "${D7预防措施5}": "建立持续改进文化，鼓励员工提出改进建议",
                "${D7责任人5}": "周九",
                "${D7计划完成日期5}": "2025-04-15"
            },
            "预防6": {
                "${D7预防措施6}": "实施客户满意度定期调查制度",
                "${D7责任人6}": "孙八",
                "${D7计划完成日期6}": "2025-03-30"
            }
            # 6个预防措施，超过了建议的5个限制
        },
        "D8庆贺团队": {
            "${D8有效性确认}": "所有措施实施完成后，将进行全面效果评估",
            "${D8确认人}": "项目经理",
            "${D8确认完成时间}": "2025-08-15"
        }
    }


def test_data_analysis():
    """测试数据分析功能"""
    print("\n" + "="*60)
    print("🧪 测试数据分析功能")
    print("="*60)
    
    # 测试最小数据
    print("\n📊 测试最小数据分析:")
    minimal_data = create_test_data_minimal()
    analysis_minimal = analyze_data_structure(minimal_data)
    print(f"最小数据分析结果: {json.dumps(analysis_minimal, ensure_ascii=False, indent=2)}")
    
    # 测试溢出数据
    print("\n📊 测试溢出数据分析:")
    overflow_data = create_test_data_overflow()
    analysis_overflow = analyze_data_structure(overflow_data)
    print(f"溢出数据分析结果: {json.dumps(analysis_overflow, ensure_ascii=False, indent=2)}")
    
    return analysis_minimal, analysis_overflow


def test_overflow_detection():
    """测试内容溢出检测"""
    print("\n" + "="*60)
    print("🧪 测试内容溢出检测")
    print("="*60)
    
    overflow_data = create_test_data_overflow()
    analysis = analyze_data_structure(overflow_data)
    overflow_sections = detect_content_overflow(overflow_data, analysis)
    
    print(f"检测到的溢出sections: {json.dumps(overflow_sections, ensure_ascii=False, indent=2)}")
    
    return overflow_sections


def test_smart_replacements():
    """测试智能替换数据生成"""
    print("\n" + "="*60)
    print("🧪 测试智能替换数据生成")
    print("="*60)
    
    # 测试最小数据
    minimal_data = create_test_data_minimal()
    analysis_minimal = analyze_data_structure(minimal_data)
    smart_replacements_minimal = create_smart_replacements(minimal_data, analysis_minimal)
    
    print(f"最小数据智能替换 (前10项): {dict(list(smart_replacements_minimal.items())[:10])}")
    
    # 测试溢出数据
    overflow_data = create_test_data_overflow()
    analysis_overflow = analyze_data_structure(overflow_data)
    smart_replacements_overflow = create_smart_replacements(overflow_data, analysis_overflow)
    
    print(f"溢出数据智能替换 (前10项): {dict(list(smart_replacements_overflow.items())[:10])}")
    
    return smart_replacements_minimal, smart_replacements_overflow


if __name__ == "__main__":
    print("🚀 开始智能PPT/DOCX处理功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行各项测试
        test_data_analysis()
        test_overflow_detection()
        test_smart_replacements()
        
        print("\n" + "="*60)
        print("✅ 所有测试完成")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
