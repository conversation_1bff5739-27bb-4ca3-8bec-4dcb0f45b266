2300418
nohup: ignoring input
已加载 14 个文件映射
 * Serving Flask app 'app'
 * Debug mode: off
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5555
 * Running on http://*************:5555
Press CTRL+C to quit
127.0.0.1 - - [10/Jun/2025 08:55:10] "POST /process_ppt HTTP/1.1" 200 -
127.0.0.1 - - [10/Jun/2025 08:55:13] "GET / HTTP/1.1" 404 -
127.0.0.1 - - [10/Jun/2025 08:55:13] "GET /favicon.ico HTTP/1.1" 404 -
127.0.0.1 - - [10/Jun/2025 08:55:23] "GET /download/1936385f-080a-47a0-9a11-f8da086d799f HTTP/1.1************ - - [11/Jun/2025 16:48:30] "GET /download/b06f72d6-3acf-4150-b9ff-d68cc916f094 HTTP/1.1" 200 -
************ - - [11/Jun/2025 16:48:30] "GET /favicon.ico HTTP/1.1" 404 -
************ - - [11/Jun/2025 16:48:42] "GET /download/83288d38-c87b-4397-b6f7-4fe0ce7dd2f8 HTTP/1.1" 200 -
