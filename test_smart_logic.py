#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能处理逻辑测试脚本（不依赖外部库）
测试数据分析、内容溢出检测等核心逻辑
"""

import json
from datetime import datetime


def analyze_data_structure(data):
    """分析数据结构，提取动态内容信息"""
    analysis = {
        'dynamic_sections': {},
        'content_counts': {},
        'has_content': {}
    }
    
    # 分析各个D步骤的内容
    for section_key, section_data in data.items():
        if isinstance(section_data, dict):
            # 统计动态内容数量
            if section_key in ['D1建立小组', 'D3临时措施', 'D4根本原因', 'D5永久措施', 'D6措施验证', 'D7预防措施']:
                content_count = 0
                dynamic_items = []
                
                for key, value in section_data.items():
                    if isinstance(value, dict) and value:  # 非空字典
                        # 检查是否有实际内容
                        has_real_content = any(v and str(v).strip() for v in value.values() if isinstance(v, str))
                        if has_real_content:
                            content_count += 1
                            dynamic_items.append(key)
                    elif isinstance(value, list) and value:  # 非空列表
                        for item in value:
                            if isinstance(item, dict) and any(v and str(v).strip() for v in item.values() if isinstance(v, str)):
                                content_count += 1
                                dynamic_items.append(f"{key}_{len(dynamic_items)}")
                
                analysis['dynamic_sections'][section_key] = dynamic_items
                analysis['content_counts'][section_key] = content_count
                analysis['has_content'][section_key] = content_count > 0
            else:
                # 检查普通section是否有内容
                has_content = False
                if isinstance(section_data, dict):
                    has_content = any(v and str(v).strip() for v in section_data.values() if isinstance(v, str))
                analysis['has_content'][section_key] = has_content
    
    return analysis


def detect_content_overflow(data, analysis):
    """检测内容是否会溢出，需要分页"""
    overflow_sections = {}
    
    # 定义每个section的容量限制
    section_limits = {
        'D1建立小组': 6,  # 最多6个成员
        'D3临时措施': 5,  # 最多5个临时措施
        'D4根本原因': 8,  # 根本原因分析较复杂，限制较高
        'D5永久措施': 5,  # 最多5个永久措施
        'D6措施验证': 5,  # 最多5个验证
        'D7预防措施': 5   # 最多5个预防措施
    }
    
    for section_key, count in analysis['content_counts'].items():
        if section_key in section_limits and count > section_limits[section_key]:
            overflow_sections[section_key] = {
                'current_count': count,
                'limit': section_limits[section_key],
                'overflow_count': count - section_limits[section_key]
            }
    
    return overflow_sections


def create_test_data_minimal():
    """创建最小测试数据（模拟空白内容场景）"""
    return {
        "D0汇报信息": {
            "${D0标题}": "测试问题报告",
            "${D0汇报人}": "测试人员",
            "${D0汇报时间}": "2025-01-23",
            "${D0项目背景}": "这是一个测试场景"
        },
        "D1建立小组": {
            "组长": {
                "${D1组长姓名}": "张三",
                "${D1组长部门}": "测试部",
                "${D1组长职位}": "测试经理",
                "${D1组长主要职责}": "负责测试协调"
            }
            # 注意：没有成员数据，应该会清理空白行
        },
        "D2问题描述": {
            "${D2事件整体描述}": "测试问题描述",
            "5W2H": {
                "${D2何时发生}": "2025-01-23",
                "${D2何地发生}": "测试环境",
                "${D2何人发现}": "测试人员",
                "${D2为什么是这问题}": "测试原因",
                "${D2发生了什么问题}": "测试问题",
                "${D2问题如何发生}": "测试过程",
                "${D2问题影响程度}": "轻微"
            }
        },
        "D3临时措施": {
            # 完全空白的section
        },
        "D4根本原因": {
            "${D4原因小结}": "测试根本原因分析"
        },
        "D5永久措施": {
            # 完全空白的section
        },
        "D6措施验证": {
            # 完全空白的section
        },
        "D7预防措施": {
            # 完全空白的section
        },
        "D8庆贺团队": {
            "${D8有效性确认}": "测试确认",
            "${D8确认人}": "测试人员",
            "${D8确认完成时间}": "2025-01-23"
        }
    }


def create_test_data_overflow():
    """创建溢出测试数据（模拟内容过多场景）"""
    return {
        "D0汇报信息": {
            "${D0标题}": "复杂问题报告",
            "${D0汇报人}": "项目经理",
            "${D0汇报时间}": "2025-01-23",
            "${D0项目背景}": "这是一个复杂的问题场景，涉及多个方面"
        },
        "D1建立小组": {
            "组长": {
                "${D1组长姓名}": "张三",
                "${D1组长部门}": "质量部",
                "${D1组长职位}": "质量经理",
                "${D1组长主要职责}": "负责整体协调"
            },
            "成员1": {
                "${D1成员1姓名}": "李四",
                "${D1成员1部门}": "生产部",
                "${D1成员1职位}": "生产主管",
                "${D1成员1主要职责}": "生产过程控制"
            },
            "成员2": {
                "${D1成员2姓名}": "王五",
                "${D1成员2部门}": "工程部",
                "${D1成员2职位}": "工程师",
                "${D1成员2主要职责}": "技术分析"
            },
            "成员3": {
                "${D1成员3姓名}": "赵六",
                "${D1成员3部门}": "采购部",
                "${D1成员3职位}": "采购员",
                "${D1成员3主要职责}": "供应商管理"
            },
            "成员4": {
                "${D1成员4姓名}": "钱七",
                "${D1成员4部门}": "财务部",
                "${D1成员4职位}": "财务分析师",
                "${D1成员4主要职责}": "成本分析"
            },
            "成员5": {
                "${D1成员5姓名}": "孙八",
                "${D1成员5部门}": "销售部",
                "${D1成员5职位}": "销售经理",
                "${D1成员5主要职责}": "客户沟通"
            },
            "成员6": {
                "${D1成员6姓名}": "周九",
                "${D1成员6部门}": "研发部",
                "${D1成员6职位}": "研发工程师",
                "${D1成员6主要职责}": "产品改进"
            },
            "成员7": {
                "${D1成员7姓名}": "吴十",
                "${D1成员7部门}": "物流部",
                "${D1成员7职位}": "物流主管",
                "${D1成员7主要职责}": "物流协调"
            }
            # 8个成员，超过了建议的6个限制
        },
        "D3临时措施": {
            "临时措施1": {
                "${D3范围1}": "生产线A区",
                "${D3处置对策1}": "立即停产检查，更换问题批次材料",
                "${D3责任人1}": "张三",
                "${D3完成期限1}": "2025-01-24",
                "${D3状态1}": "进行中",
                "${D3进度备注1}": "已完成50%"
            },
            "临时措施2": {
                "${D3范围2}": "生产线B区",
                "${D3处置对策2}": "加强质量检验频次，每小时检查一次",
                "${D3责任人2}": "李四",
                "${D3完成期限2}": "2025-01-25",
                "${D3状态2}": "已完成",
                "${D3进度备注2}": "检验频次已调整"
            },
            "临时措施3": {
                "${D3范围3}": "生产线C区",
                "${D3处置对策3}": "临时增加人工检验岗位",
                "${D3责任人3}": "王五",
                "${D3完成期限3}": "2025-01-26",
                "${D3状态3}": "计划中",
                "${D3进度备注3}": "正在安排人员"
            },
            "临时措施4": {
                "${D3范围4}": "供应商管理",
                "${D3处置对策4}": "要求供应商提供材料质量证明",
                "${D3责任人4}": "赵六",
                "${D3完成期限4}": "2025-01-27",
                "${D3状态4}": "进行中",
                "${D3进度备注4}": "已联系主要供应商"
            },
            "临时措施5": {
                "${D3范围5}": "客户沟通",
                "${D3处置对策5}": "主动联系受影响客户，说明情况",
                "${D3责任人5}": "孙八",
                "${D3完成期限5}": "2025-01-28",
                "${D3状态5}": "已完成",
                "${D3进度备注5}": "已联系所有受影响客户"
            },
            "临时措施6": {
                "${D3范围6}": "库存管理",
                "${D3处置对策6}": "隔离问题批次产品，防止流出",
                "${D3责任人6}": "吴十",
                "${D3完成期限6}": "2025-01-29",
                "${D3状态6}": "已完成",
                "${D3进度备注6}": "问题产品已全部隔离"
            }
            # 6个临时措施，超过了建议的5个限制
        },
        "D5永久措施": {
            "措施1": {
                "${D5纠正措施1}": "建立供应商质量管理体系",
                "${D5责任人1}": "赵六",
                "${D5计划完成日期1}": "2025-03-01"
            },
            "措施2": {
                "${D5纠正措施2}": "制定设备预防性维护计划",
                "${D5责任人2}": "王五",
                "${D5计划完成日期2}": "2025-02-15"
            },
            "措施3": {
                "${D5纠正措施3}": "建立员工培训体系",
                "${D5责任人3}": "张三",
                "${D5计划完成日期3}": "2025-02-28"
            },
            "措施4": {
                "${D5纠正措施4}": "完善质量管理流程",
                "${D5责任人4}": "李四",
                "${D5计划完成日期4}": "2025-02-20"
            },
            "措施5": {
                "${D5纠正措施5}": "建立客户反馈快速响应机制",
                "${D5责任人5}": "孙八",
                "${D5计划完成日期5}": "2025-02-10"
            },
            "措施6": {
                "${D5纠正措施6}": "实施库存管理系统升级",
                "${D5责任人6}": "吴十",
                "${D5计划完成日期6}": "2025-03-15"
            }
            # 6个永久措施，超过了建议的5个限制
        }
    }


def test_data_analysis():
    """测试数据分析功能"""
    print("\n" + "="*60)
    print("🧪 测试数据分析功能")
    print("="*60)
    
    # 测试最小数据
    print("\n📊 测试最小数据分析:")
    minimal_data = create_test_data_minimal()
    analysis_minimal = analyze_data_structure(minimal_data)
    print(f"最小数据分析结果:")
    print(f"  - 动态sections: {analysis_minimal['dynamic_sections']}")
    print(f"  - 内容计数: {analysis_minimal['content_counts']}")
    print(f"  - 有内容的sections: {analysis_minimal['has_content']}")
    
    # 测试溢出数据
    print("\n📊 测试溢出数据分析:")
    overflow_data = create_test_data_overflow()
    analysis_overflow = analyze_data_structure(overflow_data)
    print(f"溢出数据分析结果:")
    print(f"  - 动态sections: {analysis_overflow['dynamic_sections']}")
    print(f"  - 内容计数: {analysis_overflow['content_counts']}")
    print(f"  - 有内容的sections: {analysis_overflow['has_content']}")
    
    return analysis_minimal, analysis_overflow


def test_overflow_detection():
    """测试内容溢出检测"""
    print("\n" + "="*60)
    print("🧪 测试内容溢出检测")
    print("="*60)
    
    overflow_data = create_test_data_overflow()
    analysis = analyze_data_structure(overflow_data)
    overflow_sections = detect_content_overflow(overflow_data, analysis)
    
    print(f"检测到的溢出sections:")
    for section, info in overflow_sections.items():
        print(f"  - {section}: 当前{info['current_count']}项，限制{info['limit']}项，溢出{info['overflow_count']}项")
    
    return overflow_sections


if __name__ == "__main__":
    print("🚀 开始智能处理逻辑测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行各项测试
        analysis_minimal, analysis_overflow = test_data_analysis()
        overflow_sections = test_overflow_detection()
        
        print("\n" + "="*60)
        print("✅ 核心逻辑测试完成")
        print("="*60)
        
        # 总结测试结果
        print("\n📋 测试总结:")
        print(f"1. 最小数据场景:")
        print(f"   - 有内容的sections: {sum(1 for v in analysis_minimal['has_content'].values() if v)}")
        print(f"   - 空白的sections: {sum(1 for v in analysis_minimal['has_content'].values() if not v)}")
        
        print(f"\n2. 溢出数据场景:")
        print(f"   - 有内容的sections: {sum(1 for v in analysis_overflow['has_content'].values() if v)}")
        print(f"   - 溢出的sections: {len(overflow_sections)}")
        print(f"   - 总溢出项数: {sum(info['overflow_count'] for info in overflow_sections.values())}")
        
        print(f"\n💡 智能处理建议:")
        print(f"   - 对于最小数据：自动隐藏空白sections，清理空白表格行")
        print(f"   - 对于溢出数据：创建额外页面，分页显示溢出内容")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
