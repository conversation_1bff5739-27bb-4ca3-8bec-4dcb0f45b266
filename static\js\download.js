// 下载页面交互功能脚本

let downloadStarted = false;

function startDownload() {
    if (downloadStarted) return;
    downloadStarted = true;
    
    const btn = document.querySelector('.download-btn');
    const progressContainer = document.getElementById('progressContainer');
    const progressText = document.getElementById('progressText');
    
    // 显示状态提示
    progressContainer.style.display = 'block';
    btn.style.display = 'none';
    
    // 显示下载状态
    progressText.innerHTML = '🔄 正在准备下载...';
    
    // 开始实际下载
    setTimeout(() => {
        progressText.innerHTML = '⬇️ 下载已开始，请查看浏览器下载区域';
        
        // 从DOM中获取下载相关信息
        const downloadId = document.querySelector('[data-download-id]').dataset.downloadId;
        const filename = document.querySelector('[data-filename]').dataset.filename;
        
        const downloadUrl = `/download_file/${downloadId}`;
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 下载开始后的提示
        setTimeout(() => {
            progressText.innerHTML = '✅ 下载指令已发送！<br><small>如果下载没有开始，请检查浏览器下载设置</small>';
            
            // 显示重新下载按钮
            setTimeout(() => {
                progressContainer.innerHTML = `
                    <div class="success-message">
                        ✅ 下载已启动！请稍等片刻。<br>
                        <small>如果下载有问题，可以点击下方按钮重新下载</small>
                    </div>
                    <button class="download-btn" onclick="location.reload()" style="font-size: 0.9rem; padding: 10px 30px;">
                        🔄 重新下载
                    </button>
                `;
            }, 3000);
        }, 1000);
    }, 500);
} 