180355
nohup: ignoring input
已加载 15 个文件映射
 * Serving Flask app 'app'
 * Debug mode: off
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5555
 * Running on http://*************:5555
Press CTRL+C t
加载DOCX文档: template.docx
扁平化后的替换数据: {'${D0标题}': '', '${D0汇报人}': '', '${D0汇报时间}': '', '${D1组长姓名}': '', '${D1组长部门}': '', '${D1组长职位}': '', '${D1组长主要职责}': '', '${D1成员1姓名}': '', '${D1成员1部门}': '', '${D1成员1职位}': '', '${D1成员1主要职责}': '', '${D2事件整体描述}': '2024年1月29日，天津环晟工厂在12线层前EL设备上线调试时，发现设备正/负极探针组件装配位置错误，导致组件引线位置无法对应，无法进行EL上电。问题由赵文英发现，影响数量为1台设备。', '${D2何时发生}': '2024年1月29日', '${D2何地发生}': '天津环晟工厂', '${D2何人发现}': '赵文英', '${D2为什么是这问题}': '设备正/负极探针组件装配位置错误，导致组件引线位置无法对应', '${D2发生了什么问题}': '设备正/负极探针组件装配位置错误', '${D2问题如何发生}': '设备上线调试时发现', '${D2问题影响程度}': '影响数量为1台设备', '${D3范围1}': '天津环晟工厂12线层前EL设备', '${D3处置对策1}': '调换设备正/负极探针组件位置', '${D3责任人1}': '武斌', '${D3完成期限1}': '2024/1/30', '${D3状态1}': '完成', '${D3进度备注1}': '成玉峰协助实施', '${D4why1}': '设备改造装配时研发及工艺未对接指导', '${D4answer1}': '研发及工艺间缺乏有效的沟通和确认', '${D4why2}': '质量未确认装配位置', '${D4answer2}': '质量检验流程不完善', '${D4why3}': '', '${D4answer3}': '', '${D4why4}': '', '${D4answer4}': '', '${D4why5}': '', '${D4answer5}': '', '${D4人原因1}': '', '${D4人原因2}': '', '${D4人原因3}': '', '${D4机原因1}': '', '${D4机原因2}': '', '${D4机原因3}': '', '${D4料原因1}': '', '${D4料原因2}': '', '${D4料原因3}': '', '${D4法原因1}': '研发未明确装配规范', '${D4法原因2}': '工艺未按SOP指导', '${D4法原因3}': '', '${D4环原因1}': '', '${D4环原因2}': '', '${D4环原因3}': '', '${D4测原因1}': '质量未验证', '${D4测原因2}': '', '${D4测原因3}': '', '${D4可能原因1}': '研发未明确装配规范', '${D4判定1}': '是', '${D4证据1}': '设备上线调试时发现装配位置错误', '${D4可能原因2}': '工艺未按SOP指导', '${D4判定2}': '是', '${D4证据2}': '工艺未提供相应的指导文件', '${D4可能原因3}': '质量未验证', '${D4判定3}': '是', '${D4证据3}': '质量检验流程中未包含装配位置的验证', '${D4原因小结}': '设备改造装配时研发及工艺未对接指导，质量未确认装配位置', '${D5纠正措施1}': '工艺出具SOP文件指导探针组件安装', '${D5责任人1}': '周淳', '${D5计划完成日期1}': '2024/2/5', '${D6措施验证1}': '车间质检对照图纸和SOP文件检验探针组件装配位置', '${D6验证人1}': '孙宇航', '${D6验证时间1}': '2024/2/6', '${D6验证结果1}': '结果符合要求', '${D7预防措施1}': '输出探针组件装配SOP文件', '${D7责任人1}': '周淳', '${D7计划完成日期1}': '2024/2/5', '${D8有效性确认}': '结果符合要求', '${D8确认人}': '孙宇航', '${D8确认完成时间}': '2024/2/6'}
开始处理DOCX中的所有占位符...
处理文档段落...
找到 41 个表格（包括嵌套表格）
处理表格 1:
表格大小: 20行 x 5列
处理单元格 [8,1]: '事件整体描述：${D2事件整体描述}'
    找到匹配: ${D2事件整体描述} -> 2024年1月29日，天津环晟工厂在12线层前EL设备上线调试时，发现设备正/负极探针组件装配位置错误，导致组件引线位置无法对应，无法进行EL上电。问题由赵文英发现，影响数量为1台设备。
    替换结果: '事件整体描述：${D2事件整体描述}' -> '事件整体描述：2024年1月29日，天津环晟工厂在12线层前EL设备上线调试时，发现设备正/负极探针组件装配位置错误，导致组件引线位置无法对应，无法进行EL上电。问题由赵文英发现，影响数量为1台设备。'
处理单元格 [12,1]: '5Why分析：
产生原因小结：${D4原因小结}'
    找到匹配: ${D4原因小结} -> 设备改造装配时研发及工艺未对接指导，质量未确认装配位置
    替换结果: '产生原因小结：${D4原因小结} ' -> '产生原因小结：设备改造装配时研发及工艺未对接指导，质量未确认装配位置 '
处理单元格 [20,1]: '确认文件内容及有效性：
${D8有效性确认}
关闭确认人/日期：${D8确认人}  ${D8确认完成时间}'
    找到匹配: ${D8有效性确认} -> 结果符合要求
    替换结果: '${D8有效性确认}' -> '结果符合要求'
    找到匹配: ${D8确认人} -> 孙宇航
    找到匹配: ${D8确认完成时间} -> 2024/2/6
    替换结果: '关闭确认人/日期：${D8确认人}  ${D8确认完成时间}' -> '关闭确认人/日期：孙宇航  2024/2/6'
表格 1 处理完成，有替换
处理表格 2:
表格大小: 8行 x 5列
处理单元格 [2,2]: '${D1组长姓名}'
    找到匹配: ${D1组长姓名} -> 
    替换结果: '${D1组长姓名}' -> ''
处理单元格 [2,3]: '${D1组长部门}'
    找到匹配: ${D1组长部门} -> 
    替换结果: '${D1组长部门}' -> ''
处理单元格 [2,4]: '${D1组长职位}'
    找到匹配: ${D1组长职位} -> 
    替换结果: '${D1组长职位}' -> ''
处理单元格 [2,5]: '${D1组长主要职责}'
    找到匹配: ${D1组长主要职责} -> 
    替换结果: '${D1组长主要职责}' -> ''
处理单元格 [3,2]: '${D1成员1姓名}'
    找到匹配: ${D1成员1姓名} -> 
    替换结果: '${D1成员1姓名}' -> ''
处理单元格 [3,3]: '${D1成员1部门}'
    找到匹配: ${D1成员1部门} -> 
    替换结果: '${D1成员1部门}' -> ''
处理单元格 [3,4]: '${D1成员1职位}'
    找到匹配: ${D1成员1职位} -> 
    替换结果: '${D1成员1职位}' -> ''
处理单元格 [3,5]: '${D1成员1主要职责}'
    找到匹配: ${D1成员1主要职责} -> 
    替换结果: '${D1成员1主要职责}' -> ''
处理单元格 [4,2]: '${D1成员2姓名}'
    删除未匹配的占位符: ['${D1成员2姓名}']
    替换结果: '${D1成员2姓名}' -> ''
处理单元格 [4,3]: '${D1成员2部门}'
    删除未匹配的占位符: ['${D1成员2部门}']
    替换结果: '${D1成员2部门}' -> ''
处理单元格 [4,4]: '${D1成员2职位}'
    删除未匹配的占位符: ['${D1成员2职位}']
    替换结果: '${D1成员2职位}' -> ''
处理单元格 [4,5]: '${D1成员2主要职责}'
    删除未匹配的占位符: ['${D1成员2主要职责}']
    替换结果: '${D1成员2主要职责}' -> ''
处理单元格 [5,2]: '${D1成员3姓名}'
    删除未匹配的占位符: ['${D1成员3姓名}']
    替换结果: '${D1成员3姓名}' -> ''
处理单元格 [5,3]: '${D1成员3部门}'
    删除未匹配的占位符: ['${D1成员3部门}']
    替换结果: '${D1成员3部门}' -> ''
处理单元格 [5,4]: '${D1成员3职位}'
    删除未匹配的占位符: ['${D1成员3职位}']
    替换结果: '${D1成员3职位}' -> ''
处理单元格 [5,5]: '${D1成员3主要职责}'
    删除未匹配的占位符: ['${D1成员3主要职责}']
    替换结果: '${D1成员3主要职责}' -> ''
处理单元格 [6,2]: '${D1成员4姓名}'
    删除未匹配的占位符: ['${D1成员4姓名}']
    替换结果: '${D1成员4姓名}' -> ''
处理单元格 [6,3]: '${D1成员4部门}'
    删除未匹配的占位符: ['${D1成员4部门}']
    替换结果: '${D1成员4部门}' -> ''
处理单元格 [6,4]: '${D1成员4职位}'
    删除未匹配的占位符: ['${D1成员4职位}']
    替换结果: '${D1成员4职位}' -> ''
处理单元格 [6,5]: '${D1成员4主要职责}'
    删除未匹配的占位符: ['${D1成员4主要职责}']
    替换结果: '${D1成员4主要职责}' -> ''
处理单元格 [7,2]: '${D1成员5姓名}'
    删除未匹配的占位符: ['${D1成员5姓名}']
    替换结果: '${D1成员5姓名}' -> ''
处理单元格 [7,3]: '${D1成员5部门}'
    删除未匹配的占位符: ['${D1成员5部门}']
    替换结果: '${D1成员5部门}' -> ''
处理单元格 [7,4]: '${D1成员5职位}'
    删除未匹配的占位符: ['${D1成员5职位}']
    替换结果: '${D1成员5职位}' -> ''
处理单元格 [7,5]: '${D1成员5主要职责}'
    删除未匹配的占位符: ['${D1成员5主要职责}']
    替换结果: '${D1成员5主要职责}' -> ''
处理单元格 [8,2]: '${D1成员6姓名}'
    删除未匹配的占位符: ['${D1成员6姓名}']
    替换结果: '${D1成员6姓名}' -> ''
处理单元格 [8,3]: '${D1成员6部门}'
    删除未匹配的占位符: ['${D1成员6部门}']
    替换结果: '${D1成员6部门}' -> ''
处理单元格 [8,4]: '${D1成员6职位}'
    删除未匹配的占位符: ['${D1成员6职位}']
    替换结果: '${D1成员6职位}' -> ''
处理单元格 [8,5]: '${D1成员6主要职责}'
    删除未匹配的占位符: ['${D1成员6主要职责}']
    替换结果: '${D1成员6主要职责}' -> ''
表格 2 处理完成，有替换
处理表格 3:
表格大小: 8行 x 5列
扁平化后的替换数据: {'${D0标题}': '', '${D0汇报人}': '', '${D0汇报时间}': '', '${D1组长姓名}': '', '${D1组长部门}': '', '${D1组长职位}': '', '${D1组长主要职责}': '', '${D1成员1姓名}': '', '${D1成员1部门}': '', '${D1成员1职位}': '', '${D1成员1主要职责}': '', '${D2事件整体描述}': '2024年1月29日，天津环晟工厂在12线层前EL设备上线调试时，发现设备正/负极探针组件装配位置错误，导致组件引线位置无法对应，无法进行EL上电。问题由赵文英发现，影响数量为1台设备。', '${D2何时发生}': '2024年1月29日', '${D2何地发生}': '天津环晟工厂', '${D2何人发现}': '赵文英', '${D2为什么是这问题}': '设备正/负极探针组件装配位置错误，导致组件引线位置无法对应', '${D2发生了什么问题}': '设备正/负极探针组件装配位置错误', '${D2问题如何发生}': '设备上线调试时发现', '${D2问题影响程度}': '影响数量为1台设备', '${D3范围1}': '天津环晟工厂12线层前EL设备', '${D3处置对策1}': '调换设备正/负极探针组件位置', '${D3责任人1}': '武斌', '${D3完成期限1}': '2024/1/30', '${D3状态1}': '完成', '${D3进度备注1}': '成玉峰协助实施', '${D4why1}': '设备改造装配时研发及工艺未对接指导', '${D4answer1}': '研发及工艺间缺乏有效的沟通和确认', '${D4why2}': '质量未确认装配位置', '${D4answer2}': '质量检验流程不完善', '${D4why3}': '', '${D4answer3}': '', '${D4why4}': '', '${D4answer4}': '', '${D4why5}': '', '${D4answer5}': '', '${D4人原因1}': '', '${D4人原因2}': '', '${D4人原因3}': '', '${D4机原因1}': '', '${D4机原因2}': '', '${D4机原因3}': '', '${D4料原因1}': '', '${D4料原因2}': '', '${D4料原因3}': '', '${D4法原因1}': '研发未明确装配规范', '${D4法原因2}': '工艺未按SOP指导', '${D4法原因3}': '', '${D4环原因1}': '', '${D4环原因2}': '', '${D4环原因3}': '', '${D4测原因1}': '质量未验证', '${D4测原因2}': '', '${D4测原因3}': '', '${D4可能原因1}': '研发未明确装配规范', '${D4判定1}': '是', '${D4证据1}': '设备上线调试时发现装配位置错误', '${D4可能原因2}': '工艺未按SOP指导', '${D4判定2}': '是', '${D4证据2}': '工艺未提供相应的指导文件', '${D4可能原因3}': '质量未验证', '${D4判定3}': '是', '${D4证据3}': '质量检验流程中未包含装配位置的验证', '${D4原因小结}': '设备改造装配时研发及工艺未对接指导，质量未确认装配位置', '${D5纠正措施1}': '工艺出具SOP文件指导探针组件安装', '${D5责任人1}': '周淳', '${D5计划完成日期1}': '2024/2/5', '${D6措施验证1}': '车间质检对照图纸和SOP文件检验探针组件装配位置', '${D6验证人1}': '孙宇航', '${D6验证时间1}': '2024/2/6', '${D6验证结果1}': '结果符合要求', '${D7预防措施1}': '输出探针组件装配SOP文件', '${D7责任人1}': '周淳', '${D7计划完成日期1}': '2024/2/5', '${D8有效性确认}': '结果符合要求', '${D8确认人}': '孙宇航', '${D8确认完成时间}': '2024/2/6'}
开始处理PPT中的所有占位符...
处理第 1 页幻灯片...
  找到占位符: ['${D0汇报时间}', '${D0汇报人}', '${D0标题}']
处理第 2 页幻灯片...
  找到占位符: ['${D1成员5主要职责}', '${D1成员6部门}', '${D1成员5姓名}', '${D1成员6主要职责}', '${D1成员2部门}', '${D1成员1姓名}', '${D1成员4姓名}', '${D1成员3部门}', '${D1成员6职位}', '${D1成员2主要职责}', '${D1成员4部门}', '${D1成员1主要职责}', '${D1成员3姓名}', '${D1成员1职位}', '${D1成员4主要职责}', '${D1成员6姓名}', '${D1成员4职位}', '${D1成员5职位}', '${D1组长部门}', '${D1成员1部门}', '${D1组长主要职责}', '${D1组长职位}', '${D1成员3职位}', '${D1成员2职位}', '${D1成员5部门}', '${D1组长姓名}', '${D1成员2姓名}', '${D1成员3主要职责}']
处理第 3 页幻灯片...
  找到占位符: ['${D2何时发生}', '${D2何地发生}', '${D2问题如何发生}', '${D2发生了什么问题}', '${D2何人发现}', '${D2问题影响程度}', '${D2事件整体描述}', '${D2为什么是这问题}']
处理第 4 页幻灯片...
  找到占位符: ['${D3处置对策3}', '${D3状态3}', '${D3状态1}', '${D3范围3}', '${D3进度备注3}', '${D3进度备注1}', '${D3处置对策4}', '${D3处置对策2}', '${D3范围4}', '${D3完成期限4}', '${D3状态4}', '${D3状态2}', '${D3范围1}', '${D3责任人2}', '${D3完成期限3}', '${D3完成期限2}', '${D3进度备注2}', '${D3责任人3}', '${D3责任人4}', '${D3进度备注4}', '${D3完成期限1}', '${D3范围2}', '${D3责任人1}', '${D3处置对策1}']
表格 3 处理完成，无替换
处理表格 4:
表格大小: 8行 x 5列
处理第 5 页幻灯片...
处理第 6 页幻灯片...
处理第 7 页幻灯片...
  找到占位符: ['${D4判定5}', '${D4证据5}', '${D4证据1}', '${D4可能原因1}', '${D4原因小结}', '${D4可能原因4}', '${D4可能原因7}', '${D4判定2}', '${D4判定1}', '${D4判定3}', '${D4可能原因3}', '${D4可能原因5}', '${D4证据4}', '${D4可能原因2}', '${D4证据7}', '${D4判定7}', '${D4证据6}', '${D4判定6}', '${D4判定4}', '${D4证据3}', '${D4证据2}', '${D4可能原因6}']
处理第 8 页幻灯片...
  找到占位符: ['${D5责任人3}', '${D5纠正措施1}', '${D5计划完成日期2}', '${D5计划完成日期3}', '${D5责任人1}', '${D5责任人2}', '${D5责任人4}', '${D5纠正措施3}', '${D5计划完成日期4}', '${D5纠正措施2}', '${D5纠正措施4}', '${D5计划完成日期1}']
处理第 9 页幻灯片...
  找到占位符: ['${D6措施验证2}', '${D6验证结果2}', '${D6措施验证3}', '${D6验证人4}', '${D6验证时间4}', '${D6验证结果3}', '${D6验证结果4}', '${D6验证时间3}', '${D6验证人3}', '${D6验证结果1}', '${D6措施验证4}', '${D6验证时间1}', '${D6验证人1}', '${D6验证人2}', '${D6验证时间2}', '${D6措施验证1}']
处理第 10 页幻灯片...
  找到占位符: ['${D7计划完成日期3}', '${D7责任人1}', '${D7预防措施2}', '${D7预防措施3}', '${D7责任人3}', '${D7预防措施4}', '${D7计划完成日期1}', '${D7计划完成日期4}', '${D7责任人2}', '${D7计划完成日期2}', '${D7预防措施1}', '${D7责任人4}']
表格 4 处理完成，无替换
处理表格 5:
表格大小: 8行 x 5列
处理第 11 页幻灯片...
  找到占位符: ['${D8确认人}', '${D8有效性确认}', '${D8确认完成时间}']
处理第 12 页幻灯片...
PPT处理完成
表格 5 处理完成，无替换
处理表格 6:
表格大小: 8行 x 5列
表格 6 处理完成，无替换
处理表格 7:
表格大小: 8行 x 2列
处理单元格 [2,2]: '${D2何时发生}'
    找到匹配: ${D2何时发生} -> 2024年1月29日
    替换结果: '${D2何时发生}' -> '2024年1月29日'
处理单元格 [3,2]: '${D2何地发生}'
    找到匹配: ${D2何地发生} -> 天津环晟工厂
    替换结果: '${D2何地发生}' -> '天津环晟工厂'
处理单元格 [4,2]: '${D2何人发现}'
    找到匹配: ${D2何人发现} -> 赵文英
    替换结果: '${D2何人发现}' -> '赵文英'
处理单元格 [5,2]: '${D2为什么是这问题}'
    找到匹配: ${D2为什么是这问题} -> 设备正/负极探针组件装配位置错误，导致组件引线位置无法对应
    替换结果: '${D2为什么是这问题}' -> '设备正/负极探针组件装配位置错误，导致组件引线位置无法对应'
处理单元格 [6,2]: '${D2发生了什么问题}'
    找到匹配: ${D2发生了什么问题} -> 设备正/负极探针组件装配位置错误
    替换结果: '${D2发生了什么问题}' -> '设备正/负极探针组件装配位置错误'
处理单元格 [7,2]: '${D2问题如何发生}'
    找到匹配: ${D2问题如何发生} -> 设备上线调试时发现
    替换结果: '${D2问题如何发生}' -> '设备上线调试时发现'
处理单元格 [8,2]: '${D2问题影响程度}'
    找到匹配: ${D2问题影响程度} -> 影响数量为1台设备
    替换结果: '${D2问题影响程度}' -> '影响数量为1台设备'
表格 7 处理完成，有替换
处理表格 8:
表格大小: 8行 x 2列
表格 8 处理完成，无替换
处理表格 9:
表格大小: 8行 x 2列
表格 9 处理完成，无替换
处理表格 10:
表格大小: 8行 x 2列
表格 10 处理完成，无替换
处理表格 11:
表格大小: 8行 x 2列
表格 11 处理完成，无替换
处理表格 12:
表格大小: 5行 x 7列
处理单元格 [2,2]: '${D3范围1}'
    找到匹配: ${D3范围1} -> 天津环晟工厂12线层前EL设备
    替换结果: '${D3范围1}' -> '天津环晟工厂12线层前EL设备'
处理单元格 [2,3]: '${D3处置对策1}'
    找到匹配: ${D3处置对策1} -> 调换设备正/负极探针组件位置
    替换结果: '${D3处置对策1}' -> '调换设备正/负极探针组件位置'
处理单元格 [2,4]: '${D3责任人1}'
    找到匹配: ${D3责任人1} -> 武斌
    替换结果: '${D3责任人1}' -> '武斌'
处理单元格 [2,5]: '${D3完成期限1}'
    找到匹配: ${D3完成期限1} -> 2024/1/30
    替换结果: '${D3完成期限1}' -> '2024/1/30'
处理单元格 [2,6]: '${D3状态1}'
    找到匹配: ${D3状态1} -> 完成
    替换结果: '${D3状态1}' -> '完成'
处理单元格 [2,7]: '${D3进度备注1}'
    找到匹配: ${D3进度备注1} -> 成玉峰协助实施
    替换结果: '${D3进度备注1}' -> '成玉峰协助实施'
处理单元格 [3,2]: '${D3范围2}'
    删除未匹配的占位符: ['${D3范围2}']
    替换结果: '${D3范围2}' -> ''
处理单元格 [3,3]: '${D3处置对策2}'
    删除未匹配的占位符: ['${D3处置对策2}']
    替换结果: '${D3处置对策2}' -> ''
处理单元格 [3,4]: '${D3责任人2}'
    删除未匹配的占位符: ['${D3责任人2}']
    替换结果: '${D3责任人2}' -> ''
处理单元格 [3,5]: '${D3完成期限2}'
    删除未匹配的占位符: ['${D3完成期限2}']
    替换结果: '${D3完成期限2}' -> ''
处理单元格 [3,6]: '${D3状态2}'
    删除未匹配的占位符: ['${D3状态2}']
    替换结果: '${D3状态2}' -> ''
处理单元格 [3,7]: '${D3进度备注2}'
    删除未匹配的占位符: ['${D3进度备注2}']
    替换结果: '${D3进度备注2}' -> ''
处理单元格 [4,2]: '${D3范围3}'
    删除未匹配的占位符: ['${D3范围3}']
    替换结果: '${D3范围3}' -> ''
处理单元格 [4,3]: '${D3处置对策3}'
    删除未匹配的占位符: ['${D3处置对策3}']
    替换结果: '${D3处置对策3}' -> ''
处理单元格 [4,4]: '${D3责任人3}'
    删除未匹配的占位符: ['${D3责任人3}']
    替换结果: '${D3责任人3}' -> ''
处理单元格 [4,5]: '${D3完成期限3}'
    删除未匹配的占位符: ['${D3完成期限3}']
    替换结果: '${D3完成期限3}' -> ''
处理单元格 [4,6]: '${D3状态3}'
    删除未匹配的占位符: ['${D3状态3}']
    替换结果: '${D3状态3}' -> ''
处理单元格 [4,7]: '${D3进度备注3}'
    删除未匹配的占位符: ['${D3进度备注3}']
    替换结果: '${D3进度备注3}' -> ''
处理单元格 [5,2]: '${D3范围4}'
    删除未匹配的占位符: ['${D3范围4}']
    替换结果: '${D3范围4}' -> ''
处理单元格 [5,3]: '${D3处置对策4}'
    删除未匹配的占位符: ['${D3处置对策4}']
    替换结果: '${D3处置对策4}' -> ''
处理单元格 [5,4]: '${D3责任人4}'
    删除未匹配的占位符: ['${D3责任人4}']
    替换结果: '${D3责任人4}' -> ''
处理单元格 [5,5]: '${D3完成期限4}'
    删除未匹配的占位符: ['${D3完成期限4}']
    替换结果: '${D3完成期限4}' -> ''
处理单元格 [5,6]: '${D3状态4}'
    删除未匹配的占位符: ['${D3状态4}']
    替换结果: '${D3状态4}' -> ''
处理单元格 [5,7]: '${D3进度备注4}'
    删除未匹配的占位符: ['${D3进度备注4}']
    替换结果: '${D3进度备注4}' -> ''
表格 12 处理完成，有替换
处理表格 13:
表格大小: 5行 x 7列
表格 13 处理完成，无替换
处理表格 14:
表格大小: 5行 x 7列
表格 14 处理完成，无替换
处理表格 15:
表格大小: 5行 x 7列
表格 15 处理完成，无替换
处理表格 16:
表格大小: 5行 x 7列
表格 16 处理完成，无替换
处理表格 17:
表格大小: 6行 x 3列
处理单元格 [2,2]: '${D4why1}'
    找到匹配: ${D4why1} -> 设备改造装配时研发及工艺未对接指导
    替换结果: '${D4why1}' -> '设备改造装配时研发及工艺未对接指导'
处理单元格 [2,3]: '${D4answer1}'
    找到匹配: ${D4answer1} -> 研发及工艺间缺乏有效的沟通和确认
    替换结果: '${D4answer1}' -> '研发及工艺间缺乏有效的沟通和确认'
处理单元格 [3,2]: '${D4why2}'
    找到匹配: ${D4why2} -> 质量未确认装配位置
    替换结果: '${D4why2}' -> '质量未确认装配位置'
处理单元格 [3,3]: '${D4answer2}'
    找到匹配: ${D4answer2} -> 质量检验流程不完善
    替换结果: '${D4answer2}' -> '质量检验流程不完善'
处理单元格 [4,2]: '${D4why3}'
    找到匹配: ${D4why3} -> 
    替换结果: '${D4why3}' -> ''
处理单元格 [4,3]: '${D4answer3}'
    找到匹配: ${D4answer3} -> 
    替换结果: '${D4answer3}' -> ''
处理单元格 [5,2]: '${D4why4}'
    找到匹配: ${D4why4} -> 
    替换结果: '${D4why4}' -> ''
处理单元格 [5,3]: '${D4answer4}'
    找到匹配: ${D4answer4} -> 
    替换结果: '${D4answer4}' -> ''
处理单元格 [6,2]: '${D4why5}'
    找到匹配: ${D4why5} -> 
    替换结果: '${D4why5}' -> ''
处理单元格 [6,3]: '${D4answer5}'
    找到匹配: ${D4answer5} -> 
    替换结果: '${D4answer5}' -> ''
表格 17 处理完成，有替换
处理表格 18:
表格大小: 8行 x 4列
处理单元格 [2,2]: '${D4可能原因1}'
    找到匹配: ${D4可能原因1} -> 研发未明确装配规范
    替换结果: '${D4可能原因1}' -> '研发未明确装配规范'
处理单元格 [2,3]: '${D4判定1}'
    找到匹配: ${D4判定1} -> 是
    替换结果: '${D4判定1}' -> '是'
处理单元格 [2,4]: '${D4证据1}'
    找到匹配: ${D4证据1} -> 设备上线调试时发现装配位置错误
    替换结果: '${D4证据1}' -> '设备上线调试时发现装配位置错误'
处理单元格 [3,2]: '${D4可能原因2}'
    找到匹配: ${D4可能原因2} -> 工艺未按SOP指导
    替换结果: '${D4可能原因2}' -> '工艺未按SOP指导'
处理单元格 [3,3]: '${D4判定2}'
    找到匹配: ${D4判定2} -> 是
    替换结果: '${D4判定2}' -> '是'
处理单元格 [3,4]: '${D4证据2}'
    找到匹配: ${D4证据2} -> 工艺未提供相应的指导文件
    替换结果: '${D4证据2}' -> '工艺未提供相应的指导文件'
处理单元格 [4,2]: '${D4可能原因3}'
    找到匹配: ${D4可能原因3} -> 质量未验证
    替换结果: '${D4可能原因3}' -> '质量未验证'
处理单元格 [4,3]: '${D4判定3}'
    找到匹配: ${D4判定3} -> 是
    替换结果: '${D4判定3}' -> '是'
处理单元格 [4,4]: '${D4证据3}'
    找到匹配: ${D4证据3} -> 质量检验流程中未包含装配位置的验证
172.18.0.6 - - [19/Jun/2025 13:15:39] "POST /process_ppt HTTP/1.1" 200 -
    替换结果: '${D4证据3}' -> '质量检验流程中未包含装配位置的验证'
处理单元格 [5,2]: '${D4可能原因4}'
    删除未匹配的占位符: ['${D4可能原因4}']
    替换结果: '${D4可能原因4}' -> ''
处理单元格 [5,3]: '${D4判定4}'
    删除未匹配的占位符: ['${D4判定4}']
    替换结果: '${D4判定4}' -> ''
处理单元格 [5,4]: '${D4证据4}'
    删除未匹配的占位符: ['${D4证据4}']
    替换结果: '${D4证据4}' -> ''
处理单元格 [6,2]: '${D4可能原因5}'
    删除未匹配的占位符: ['${D4可能原因5}']
    替换结果: '${D4可能原因5}' -> ''
处理单元格 [6,3]: '${D4判定5}'
    删除未匹配的占位符: ['${D4判定5}']
    替换结果: '${D4判定5}' -> ''
处理单元格 [6,4]: '${D4证据5}'
    删除未匹配的占位符: ['${D4证据5}']
    替换结果: '${D4证据5}' -> ''
处理单元格 [7,2]: '${D4可能原因6}'
    删除未匹配的占位符: ['${D4可能原因6}']
    替换结果: '${D4可能原因6}' -> ''
处理单元格 [7,3]: '${D4判定6}'
    删除未匹配的占位符: ['${D4判定6}']
    替换结果: '${D4判定6}' -> ''
处理单元格 [7,4]: '${D4证据6}'
    删除未匹配的占位符: ['${D4证据6}']
    替换结果: '${D4证据6}' -> ''
处理单元格 [8,2]: '${D4可能原因7}'
    删除未匹配的占位符: ['${D4可能原因7}']
    替换结果: '${D4可能原因7}' -> ''
处理单元格 [8,3]: '${D4判定7}'
    删除未匹配的占位符: ['${D4判定7}']
    替换结果: '${D4判定7}' -> ''
处理单元格 [8,4]: '${D4证据7}'
    删除未匹配的占位符: ['${D4证据7}']
    替换结果: '${D4证据7}' -> ''
表格 18 处理完成，有替换
处理表格 19:
表格大小: 6行 x 3列
表格 19 处理完成，无替换
处理表格 20:
表格大小: 8行 x 4列
表格 20 处理完成，无替换
处理表格 21:
表格大小: 6行 x 3列
表格 21 处理完成，无替换
处理表格 22:
表格大小: 8行 x 4列
表格 22 处理完成，无替换
处理表格 23:
表格大小: 6行 x 3列
表格 23 处理完成，无替换
处理表格 24:
表格大小: 8行 x 4列
表格 24 处理完成，无替换
处理表格 25:
表格大小: 6行 x 3列
表格 25 处理完成，无替换
处理表格 26:
表格大小: 8行 x 4列
表格 26 处理完成，无替换
处理表格 27:
表格大小: 5行 x 4列
处理单元格 [2,2]: '${D5纠正措施1}'
    找到匹配: ${D5纠正措施1} -> 工艺出具SOP文件指导探针组件安装
    替换结果: '${D5纠正措施1}' -> '工艺出具SOP文件指导探针组件安装'
处理单元格 [2,3]: '${D5责任人1}'
    找到匹配: ${D5责任人1} -> 周淳
    替换结果: '${D5责任人1}' -> '周淳'
处理单元格 [2,4]: '${D5计划完成日期1}'
    找到匹配: ${D5计划完成日期1} -> 2024/2/5
    替换结果: '${D5计划完成日期1}' -> '2024/2/5'
处理单元格 [3,2]: '${D5纠正措施2}'
    删除未匹配的占位符: ['${D5纠正措施2}']
    替换结果: '${D5纠正措施2}' -> ''
处理单元格 [3,3]: '${D5责任人2}'
    删除未匹配的占位符: ['${D5责任人2}']
    替换结果: '${D5责任人2}' -> ''
处理单元格 [3,4]: '${D5计划完成日期2}'
    删除未匹配的占位符: ['${D5计划完成日期2}']
    替换结果: '${D5计划完成日期2}' -> ''
处理单元格 [4,2]: '${D5纠正措施3}'
    删除未匹配的占位符: ['${D5纠正措施3}']
    替换结果: '${D5纠正措施3}' -> ''
处理单元格 [4,3]: '${D5责任人3}'
    删除未匹配的占位符: ['${D5责任人3}']
    替换结果: '${D5责任人3}' -> ''
处理单元格 [4,4]: '${D5计划完成日期3}'
    删除未匹配的占位符: ['${D5计划完成日期3}']
    替换结果: '${D5计划完成日期3}' -> ''
处理单元格 [5,2]: '${D5纠正措施4}'
    删除未匹配的占位符: ['${D5纠正措施4}']
    替换结果: '${D5纠正措施4}' -> ''
处理单元格 [5,3]: '${D5责任人4}'
    删除未匹配的占位符: ['${D5责任人4}']
    替换结果: '${D5责任人4}' -> ''
处理单元格 [5,4]: '${D5计划完成日期4}'
    删除未匹配的占位符: ['${D5计划完成日期4}']
    替换结果: '${D5计划完成日期4}' -> ''
表格 27 处理完成，有替换
处理表格 28:
表格大小: 5行 x 4列
表格 28 处理完成，无替换
处理表格 29:
表格大小: 5行 x 4列
表格 29 处理完成，无替换
处理表格 30:
表格大小: 5行 x 4列
表格 30 处理完成，无替换
处理表格 31:
表格大小: 5行 x 4列
表格 31 处理完成，无替换
处理表格 32:
表格大小: 5行 x 5列
处理单元格 [2,2]: '${D6措施验证1}'
    找到匹配: ${D6措施验证1} -> 车间质检对照图纸和SOP文件检验探针组件装配位置
    替换结果: '${D6措施验证1}' -> '车间质检对照图纸和SOP文件检验探针组件装配位置'
处理单元格 [2,3]: '${D6验证人1}'
    找到匹配: ${D6验证人1} -> 孙宇航
    替换结果: '${D6验证人1}' -> '孙宇航'
处理单元格 [2,4]: '${D6验证时间1}'
    找到匹配: ${D6验证时间1} -> 2024/2/6
    替换结果: '${D6验证时间1}' -> '2024/2/6'
处理单元格 [2,5]: '${D6验证结果1}'
    找到匹配: ${D6验证结果1} -> 结果符合要求
    替换结果: '${D6验证结果1}' -> '结果符合要求'
处理单元格 [3,2]: '${D6措施验证2}'
    删除未匹配的占位符: ['${D6措施验证2}']
    替换结果: '${D6措施验证2}' -> ''
处理单元格 [3,3]: '${D6验证人2}'
    删除未匹配的占位符: ['${D6验证人2}']
    替换结果: '${D6验证人2}' -> ''
处理单元格 [3,4]: '${D6验证时间2}'
    删除未匹配的占位符: ['${D6验证时间2}']
    替换结果: '${D6验证时间2}' -> ''
处理单元格 [3,5]: '${D6验证结果2}'
    删除未匹配的占位符: ['${D6验证结果2}']
    替换结果: '${D6验证结果2}' -> ''
处理单元格 [4,2]: '${D6措施验证3}'
    删除未匹配的占位符: ['${D6措施验证3}']
    替换结果: '${D6措施验证3}' -> ''
处理单元格 [4,3]: '${D6验证人3}'
    删除未匹配的占位符: ['${D6验证人3}']
    替换结果: '${D6验证人3}' -> ''
处理单元格 [4,4]: '${D6验证时间3}'
    删除未匹配的占位符: ['${D6验证时间3}']
    替换结果: '${D6验证时间3}' -> ''
处理单元格 [4,5]: '${D6验证结果3}'
    删除未匹配的占位符: ['${D6验证结果3}']
    替换结果: '${D6验证结果3}' -> ''
处理单元格 [5,2]: '${D6措施验证4}'
    删除未匹配的占位符: ['${D6措施验证4}']
    替换结果: '${D6措施验证4}' -> ''
处理单元格 [5,3]: '${D6验证人4}'
    删除未匹配的占位符: ['${D6验证人4}']
    替换结果: '${D6验证人4}' -> ''
处理单元格 [5,4]: '${D6验证时间4}'
    删除未匹配的占位符: ['${D6验证时间4}']
    替换结果: '${D6验证时间4}' -> ''
处理单元格 [5,5]: '${D6验证结果4}'
    删除未匹配的占位符: ['${D6验证结果4}']
    替换结果: '${D6验证结果4}' -> ''
表格 32 处理完成，有替换
处理表格 33:
表格大小: 5行 x 5列
表格 33 处理完成，无替换
处理表格 34:
表格大小: 5行 x 5列
表格 34 处理完成，无替换
处理表格 35:
表格大小: 5行 x 5列
表格 35 处理完成，无替换
处理表格 36:
表格大小: 5行 x 5列
表格 36 处理完成，无替换
处理表格 37:
表格大小: 5行 x 4列
处理单元格 [2,2]: '${D7预防措施1}'
    找到匹配: ${D7预防措施1} -> 输出探针组件装配SOP文件
    替换结果: '${D7预防措施1}' -> '输出探针组件装配SOP文件'
处理单元格 [2,3]: '${D7责任人1}'
    找到匹配: ${D7责任人1} -> 周淳
    替换结果: '${D7责任人1}' -> '周淳'
处理单元格 [2,4]: '${D7计划完成日期1}'
    找到匹配: ${D7计划完成日期1} -> 2024/2/5
    替换结果: '${D7计划完成日期1}' -> '2024/2/5'
处理单元格 [3,2]: '${D7预防措施2}'
    删除未匹配的占位符: ['${D7预防措施2}']
    替换结果: '${D7预防措施2}' -> ''
处理单元格 [3,3]: '${D7责任人2}'
    删除未匹配的占位符: ['${D7责任人2}']
172.18.0.6 - - [19/Jun/2025 13:15:39] "POST /process_docx HTTP/1.1" 200 -
10.60.13.200 - - [19/Jun/2025 13:15:42] "GET /download/22c1c594-7fa7-4906-b6c7-ff8b7bfd216e HTTP/1.1" 200 -
10.60.13.200 - - [19/Jun/2025 13:15:42] "GET /favicon.ico HTTP/1.1" 404 -
10.60.13.200 - - [19/Jun/2025 13:15:44] "GET /download_file/22c1c594-7fa7-4906-b6c7-ff8b7bfd216e HTTP/1.1" 200 -
    替换结果: '${D7责任人2}' -> ''
处理单元格 [3,4]: '${D7计划完成日期2}'
    删除未匹配的占位符: ['${D7计划完成日期2}']
    替换结果: '${D7计划完成日期2}' -> ''
处理单元格 [4,2]: '${D7预防措施3}'
    删除未匹配的占位符: ['${D7预防措施3}']
    替换结果: '${D7预防措施3}' -> ''
处理单元格 [4,3]: '${D7责任人3}'
    删除未匹配的占位符: ['${D7责任人3}']
    替换结果: '${D7责任人3}' -> ''
处理单元格 [4,4]: '${D7计划完成日期3}'
    删除未匹配的占位符: ['${D7计划完成日期3}']
    替换结果: '${D7计划完成日期3}' -> ''
处理单元格 [5,2]: '${D7预防措施4}'
    删除未匹配的占位符: ['${D7预防措施4}']
    替换结果: '${D7预防措施4}' -> ''
处理单元格 [5,3]: '${D7责任人4}'
    删除未匹配的占位符: ['${D7责任人4}']
    替换结果: '${D7责任人4}' -> ''
处理单元格 [5,4]: '${D7计划完成日期4}'
    删除未匹配的占位符: ['${D7计划完成日期4}']
    替换结果: '${D7计划完成日期4}' -> ''
表格 37 处理完成，有替换
处理表格 38:
表格大小: 5行 x 4列
表格 38 处理完成，无替换
处理表格 39:
表格大小: 5行 x 4列
表格 39 处理完成，无替换
处理表格 40:
表格大小: 5行 x 4列
表格 40 处理完成，无替换
处理表格 41:
表格大小: 5行 x 4列
表格 41 处理完成，无替换
DOCX处理完成
文件已保存: generated_docx/20250619_131539_451f3a4d.docx

加载DOCX文档: template.docx
扁平化后的替换数据: {'${标题}': '探针组件装配错误问题分析与改进报告', '${汇报人}': '武斌', '${汇报时间}': '2024年2月6日', '${D1组长姓名}': '武斌', '${D1组长部门}': '研发部', '${D1组长职位}': '', '${D1组长主要职责}': '负责技术分析与措施制定', '${D1成员1姓名}': '周淳', '${D1成员1部门}': '工艺部', '${D1成员1职位}': '', '${D1成员1主要职责}': '负责SOP文件编制', '${D1成员2姓名}': '赵文英', '${D1成员2部门}': '生产部', '${D1成员2职位}': '', '${D1成员2主要职责}': '负责问题发现与反馈', '${D2事件整体描述}': '设备上线时发现探针组件位置与组件引线位置不匹配，导致探针组件装配错误', '${D2何时发生}': '2024年1月29日', '${D2何地发生}': '天津环晟12线层前设备', '${D2何人发现}': '赵文英', '${D2为什么是这问题}': '装配时未按图纸要求操作', '${D2发生了什么问题}': '设备上线时发现探针组件位置与组件引线位置不匹配，导致探针组件装配错误', '${D2问题如何发生}': '装配时未按图纸要求操作，SOP文件未明确左右向EL设备的探针装配位置', '${D2问题影响程度}': '1台设备装配错误，影响设备正常运行，需返工调整', '${D3范围1}': '工厂成品', '${D3处置对策1}': '调换设备正/负探针组件在设备上的位置', '${D3责任人1}': '武斌', '${D3完成期限1}': '2024/1/30', '${D3状态1}': '完成', '${D3进度备注1}': '由成玉峰执行调换，设备恢复运行', '${D4why1}': '为什么探针组件位置错误？', '${D4answer1}': '装配时未按图纸要求操作', '${D4why2}': '为什么未按图纸操作？', '${D4answer2}': 'SOP文件未明确左右向EL设备的探针装配位置', '${D4why3}': '为什么SOP未明确？', '${D4answer3}': '研发与工艺部门未对接确认设备改造后的装配要求', '${D4why4}': '为什么未对接？', '${D4answer4}': '跨部门沟通流程缺失，未形成书面确认', '${D4why5}': '为什么流程缺失？', '${D4answer5}': '缺乏针对设备改造的标准化协作机制', '${D4人原因1}': '操作人员未接受新设备装配培训', '${D4人原因2}': '质量人员未进行装配确认', '${D4人原因3}': '研发未提供明确指导', '${D4机原因1}': '设备改造后未更新装配工装定位装置', '${D4机原因2}': '', '${D4机原因3}': '', '${D4料原因1}': '探针组件无方向标识，易混淆正负极', '${D4料原因2}': '', '${D4料原因3}': '', '${D4法原因1}': '缺乏设备改造后的装配SOP文件', '${D4法原因2}': '', '${D4法原因3}': '', '${D4环原因1}': '装配现场无图纸悬挂，依赖个人记忆', '${D4环原因2}': '', '${D4环原因3}': '', '${D4测原因1}': '质检未对探针组件位置进行全检', '${D4测原因2}': '', '${D4测原因3}': '', '${D4可能原因1}': '研发与工艺未对接确认装配位置', '${D4判定1}': '主因', '${D4证据1}': '未发送装配确认邮件', '${D4可能原因2}': '缺乏装配SOP文件', '${D4判定2}': '次因', '${D4证据2}': '无书面指导文件', '${D4可能原因3}': '质检未进行装配位置确认', '${D4判定3}': '次因', '${D4证据3}': '质检记录无相关检查项', '${D4原因小结}': '研发与工艺未对接确认装配位置，导致SOP文件不明确，质检未进行装配位置确认', '${D5纠正措施1}': '工艺部编制《探针组件装配SOP》文件并下发至各装配岗位', '${D5责任人1}': '周淳', '${D5计划完成日期1}': '2024/2/5', '${D6措施验证1}': '车间质检对照图纸和SOP文件对探针组件装配位置进行100%检验', '${D6验证人1}': '孙宇航', '${D6验证时间1}': '2024/2/6', '${D6验证结果1}': '连续3台设备装配位置100%符合要求', '${D7预防措施1}': '建立设备改造项目跨部门协作流程', '${D7责任人1}': '武斌', '${D7计划完成日期1}': '2024/2/20', '${D8有效性确认}': '问题未再发生，预防措施已落地', '${D8确认人}': '武斌', '${D8确认完成时间}': '2024/2/28'}
开始处理DOCX中的所有占位符...
处理文档段落...
找到 41 个表格（包括嵌套表格）
处理表格 1:
表格大小: 20行 x 5列
处理单元格 [8,1]: '事件整体描述：${D2事件整体描述}'
    找到匹配: ${D2事件整体描述} -> 设备上线时发现探针组件位置与组件引线位置不匹配，导致探针组件装配错误
    替换结果: '事件整体描述：${D2事件整体描述}' -> '事件整体描述：设备上线时发现探针组件位置与组件引线位置不匹配，导致探针组件装配错误'
处理单元格 [12,1]: '5Why分析：
产生原因小结：${D4原因小结}'
    找到匹配: ${D4原因小结} -> 研发与工艺未对接确认装配位置，导致SOP文件不明确，质检未进行装配位置确认
    替换结果: '产生原因小结：${D4原因小结} ' -> '产生原因小结：研发与工艺未对接确认装配位置，导致SOP文件不明确，质检未进行装配位置确认 '
处理单元格 [20,1]: '确认文件内容及有效性：
${D8有效性确认}
关闭确认人/日期：${D8确认人}  ${D8确认完成时间}'
    找到匹配: ${D8有效性确认} -> 问题未再发生，预防措施已落地
    替换结果: '${D8有效性确认}' -> '问题未再发生，预防措施已落地'
    找到匹配: ${D8确认人} -> 武斌
    找到匹配: ${D8确认完成时间} -> 2024/2/28
    替换结果: '关闭确认人/日期：${D8确认人}  ${D8确认完成时间}' -> '关闭确认人/日期：武斌  2024/2/28'
表格 1 处理完成，有替换
处理表格 2:
表格大小: 8行 x 5列
处理单元格 [2,2]: '${D1组长姓名}'
    找到匹配: ${D1组长姓名} -> 武斌
    替换结果: '${D1组长姓名}' -> '武斌'
处理单元格 [2,3]: '${D1组长部门}'
    找到匹配: ${D1组长部门} -> 研发部
    替换结果: '${D1组长部门}' -> '研发部'
处理单元格 [2,4]: '${D1组长职位}'
    找到匹配: ${D1组长职位} -> 
    替换结果: '${D1组长职位}' -> ''
处理单元格 [2,5]: '${D1组长主要职责}'
    找到匹配: ${D1组长主要职责} -> 负责技术分析与措施制定
    替换结果: '${D1组长主要职责}' -> '负责技术分析与措施制定'
处理单元格 [3,2]: '${D1成员1姓名}'
    找到匹配: ${D1成员1姓名} -> 周淳
    替换结果: '${D1成员1姓名}' -> '周淳'
处理单元格 [3,3]: '${D1成员1部门}'
    找到匹配: ${D1成员1部门} -> 工艺部
    替换结果: '${D1成员1部门}' -> '工艺部'
处理单元格 [3,4]: '${D1成员1职位}'
    找到匹配: ${D1成员1职位} -> 
    替换结果: '${D1成员1职位}' -> ''
处理单元格 [3,5]: '${D1成员1主要职责}'
    找到匹配: ${D1成员1主要职责} -> 负责SOP文件编制
    替换结果: '${D1成员1主要职责}' -> '负责SOP文件编制'
处理单元格 [4,2]: '${D1成员2姓名}'
    找到匹配: ${D1成员2姓名} -> 赵文英
    替换结果: '${D1成员2姓名}' -> '赵文英'
处理单元格 [4,3]: '${D1成员2部门}'
    找到匹配: ${D1成员2部门} -> 生产部
    替换结果: '${D1成员2部门}' -> '生产部'
处理单元格 [4,4]: '${D1成员2职位}'
    找到匹配: ${D1成员2职位} -> 
    替换结果: '${D1成员2职位}' -> ''
处理单元格 [4,5]: '${D1成员2主要职责}'
    找到匹配: ${D1成员2主要职责} -> 负责问题发现与反馈
    替换结果: '${D1成员2主要职责}' -> '负责问题发现与反馈'
处理单元格 [5,2]: '${D1成员3姓名}'
    删除未匹配的占位符: ['${D1成员3姓名}']
    替换结果: '${D1成员3姓名}' -> ''
处理单元格 [5,3]: '${D1成员3部门}'
    删除未匹配的占位符: ['${D1成员3部门}']
    替换结果: '${D1成员3部门}' -> ''
处理单元格 [5,4]: '${D1成员3职位}'
    删除未匹配的占位符: ['${D1成员3职位}']
    替换结果: '${D1成员3职位}' -> ''
处理单元格 [5,5]: '${D1成员3主要职责}'
    删除未匹配的占位符: ['${D1成员3主要职责}']
    替换结果: '${D1成员3主要职责}' -> ''
处理单元格 [6,2]: '${D1成员4姓名}'
    删除未匹配的占位符: ['${D1成员4姓名}']
    替换结果: '${D1成员4姓名}' -> ''
处理单元格 [6,3]: '${D1成员4部门}'
    删除未匹配的占位符: ['${D1成员4部门}']
    替换结果: '${D1成员4部门}' -> ''
处理单元格 [6,4]: '${D1成员4职位}'
    删除未匹配的占位符: ['${D1成员4职位}']
    替换结果: '${D1成员4职位}' -> ''
处理单元格 [6,5]: '${D1成员4主要职责}'
    删除未匹配的占位符: ['${D1成员4主要职责}']
    替换结果: '${D1成员4主要职责}' -> ''
处理单元格 [7,2]: '${D1成员5姓名}'
    删除未匹配的占位符: ['${D1成员5姓名}']
    替换结果: '${D1成员5姓名}' -> ''
处理单元格 [7,3]: '${D1成员5部门}'
    删除未匹配的占位符: ['${D1成员5部门}']
    替换结果: '${D1成员5部门}' -> ''
处理单元格 [7,4]: '${D1成员5职位}'
    删除未匹配的占位符: ['${D1成员5职位}']
    替换结果: '${D1成员5职位}' -> ''
处理单元格 [7,5]: '${D1成员5主要职责}'
    删除未匹配的占位符: ['${D1成员5主要职责}']
    替换结果: '${D1成员5主要职责}' -> ''
处理单元格 [8,2]: '${D1成员6姓名}'
    删除未匹配的占位符: ['${D1成员6姓名}']
    替换结果: '${D1成员6姓名}' -> ''
处理单元格 [8,3]: '${D1成员6部门}'
    删除未匹配的占位符: ['${D1成员6部门}']
    替换结果: '${D1成员6部门}' -> ''
处理单元格 [8,4]: '${D1成员6职位}'
    删除未匹配的占位符: ['${D1成员6职位}']
    替换结果: '${D1成员6职位}' -> ''
处理单元格 [8,5]: '${D1成员6主要职责}'
    删除未匹配的占位符: ['${D1成员6主要职责}']
    替换结果: '${D1成员6主要职责}' -> ''
表格 2 处理完成，有替换
处理表格 3:
表格大小: 8行 x 5列
表格 3 处理完成，无替换
处理表格 4:
表格大小: 8行 x 5列
表格 4 处理完成，无替换
处理表格 5:
表格大小: 8行 x 5列
表格 5 处理完成，无替换
处理表格 6:
表格大小: 8行 x 5列
表格 6 处理完成，无替换
处理表格 7:
表格大小: 8行 x 2列
处理单元格 [2,2]: '${D2何时发生}'
    找到匹配: ${D2何时发生} -> 2024年1月29日
    替换结果: '${D2何时发生}' -> '2024年1月29日'
处理单元格 [3,2]: '${D2何地发生}'
    找到匹配: ${D2何地发生} -> 天津环晟12线层前设备
    替换结果: '${D2何地发生}' -> '天津环晟12线层前设备'
处理单元格 [4,2]: '${D2何人发现}'
    找到匹配: ${D2何人发现} -> 赵文英
    替换结果: '${D2何人发现}' -> '赵文英'
处理单元格 [5,2]: '${D2为什么是这问题}'
    找到匹配: ${D2为什么是这问题} -> 装配时未按图纸要求操作
    替换结果: '${D2为什么是这问题}' -> '装配时未按图纸要求操作'
扁平化后的替换数据: {'${标题}': '探针组件装配错误问题分析与改进报告', '${汇报人}': '武斌', '${汇报时间}': '2024年2月6日', '${D1组长姓名}': '武斌', '${D1组长部门}': '研发部', '${D1组长职位}': '', '${D1组长主要职责}': '负责技术分析与措施制定', '${D1成员1姓名}': '周淳', '${D1成员1部门}': '工艺部', '${D1成员1职位}': '', '${D1成员1主要职责}': '负责SOP文件编制', '${D1成员2姓名}': '赵文英', '${D1成员2部门}': '生产部', '${D1成员2职位}': '', '${D1成员2主要职责}': '负责问题发现与反馈', '${D2事件整体描述}': '设备上线时发现探针组件位置与组件引线位置不匹配，导致探针组件装配错误', '${D2何时发生}': '2024年1月29日', '${D2何地发生}': '天津环晟12线层前设备', '${D2何人发现}': '赵文英', '${D2为什么是这问题}': '装配时未按图纸要求操作', '${D2发生了什么问题}': '设备上线时发现探针组件位置与组件引线位置不匹配，导致探针组件装配错误', '${D2问题如何发生}': '装配时未按图纸要求操作，SOP文件未明确左右向EL设备的探针装配位置', '${D2问题影响程度}': '1台设备装配错误，影响设备正常运行，需返工调整', '${D3范围1}': '工厂成品', '${D3处置对策1}': '调换设备正/负探针组件在设备上的位置', '${D3责任人1}': '武斌', '${D3完成期限1}': '2024/1/30', '${D3状态1}': '完成', '${D3进度备注1}': '由成玉峰执行调换，设备恢复运行', '${D4why1}': '为什么探针组件位置错误？', '${D4answer1}': '装配时未按图纸要求操作', '${D4why2}': '为什么未按图纸操作？', '${D4answer2}': 'SOP文件未明确左右向EL设备的探针装配位置', '${D4why3}': '为什么SOP未明确？', '${D4answer3}': '研发与工艺部门未对接确认设备改造后的装配要求', '${D4why4}': '为什么未对接？', '${D4answer4}': '跨部门沟通流程缺失，未形成书面确认', '${D4why5}': '为什么流程缺失？', '${D4answer5}': '缺乏针对设备改造的标准化协作机制', '${D4人原因1}': '操作人员未接受新设备装配培训', '${D4人原因2}': '质量人员未进行装配确认', '${D4人原因3}': '研发未提供明确指导', '${D4机原因1}': '设备改造后未更新装配工装定位装置', '${D4机原因2}': '', '${D4机原因3}': '', '${D4料原因1}': '探针组件无方向标识，易混淆正负极', '${D4料原因2}': '', '${D4料原因3}': '', '${D4法原因1}': '缺乏设备改造后的装配SOP文件', '${D4法原因2}': '', '${D4法原因3}': '', '${D4环原因1}': '装配现场无图纸悬挂，依赖个人记忆', '${D4环原因2}': '', '${D4环原因3}': '', '${D4测原因1}': '质检未对探针组件位置进行全检', '${D4测原因2}': '', '${D4测原因3}': '', '${D4可能原因1}': '研发与工艺未对接确认装配位置', '${D4判定1}': '主因', '${D4证据1}': '未发送装配确认邮件', '${D4可能原因2}': '缺乏装配SOP文件', '${D4判定2}': '次因', '${D4证据2}': '无书面指导文件', '${D4可能原因3}': '质检未进行装配位置确认', '${D4判定3}': '次因', '${D4证据3}': '质检记录无相关检查项', '${D4原因小结}': '研发与工艺未对接确认装配位置，导致SOP文件不明确，质检未进行装配位置确认', '${D5纠正措施1}': '工艺部编制《探针组件装配SOP》文件并下发至各装配岗位', '${D5责任人1}': '周淳', '${D5计划完成日期1}': '2024/2/5', '${D6措施验证1}': '车间质检对照图纸和SOP文件对探针组件装配位置进行100%检验', '${D6验证人1}': '孙宇航', '${D6验证时间1}': '2024/2/6', '${D6验证结果1}': '连续3台设备装配位置100%符合要求', '${D7预防措施1}': '建立设备改造项目跨部门协作流程', '${D7责任人1}': '武斌', '${D7计划完成日期1}': '2024/2/20', '${D8有效性确认}': '问题未再发生，预防措施已落地', '${D8确认人}': '武斌', '${D8确认完成时间}': '2024/2/28'}
开始处理PPT中的所有占位符...
处理第 1 页幻灯片...
  找到占位符: ['${D0汇报时间}', '${D0汇报人}', '${D0标题}']
处理第 2 页幻灯片...
处理单元格 [6,2]: '${D2发生了什么问题}'
  找到占位符: ['${D1成员5主要职责}', '${D1成员6部门}', '${D1成员5姓名}', '${D1成员6主要职责}', '${D1成员2部门}', '${D1成员1姓名}', '${D1成员4姓名}', '${D1成员3部门}', '${D1成员6职位}', '${D1成员2主要职责}', '${D1成员4部门}', '${D1成员1主要职责}', '${D1成员3姓名}', '${D1成员1职位}', '${D1成员4主要职责}', '${D1成员6姓名}', '${D1成员4职位}', '${D1成员5职位}', '${D1组长部门}', '${D1成员1部门}', '${D1组长主要职责}', '${D1组长职位}', '${D1成员3职位}', '${D1成员2职位}', '${D1成员5部门}', '${D1组长姓名}', '${D1成员2姓名}', '${D1成员3主要职责}']
    找到匹配: ${D2发生了什么问题} -> 设备上线时发现探针组件位置与组件引线位置不匹配，导致探针组件装配错误
    替换结果: '${D2发生了什么问题}' -> '设备上线时发现探针组件位置与组件引线位置不匹配，导致探针组件装配错误'
处理单元格 [7,2]: '${D2问题如何发生}'
    找到匹配: ${D2问题如何发生} -> 装配时未按图纸要求操作，SOP文件未明确左右向EL设备的探针装配位置
    替换结果: '${D2问题如何发生}' -> '装配时未按图纸要求操作，SOP文件未明确左右向EL设备的探针装配位置'
处理单元格 [8,2]: '${D2问题影响程度}'
处理第 3 页幻灯片...
    找到匹配: ${D2问题影响程度} -> 1台设备装配错误，影响设备正常运行，需返工调整
    替换结果: '${D2问题影响程度}' -> '1台设备装配错误，影响设备正常运行，需返工调整'
  找到占位符: ['${D2何时发生}', '${D2何地发生}', '${D2问题如何发生}', '${D2发生了什么问题}', '${D2何人发现}', '${D2问题影响程度}', '${D2事件整体描述}', '${D2为什么是这问题}']
表格 7 处理完成，有替换
处理表格 8:
表格大小: 8行 x 2列
处理第 4 页幻灯片...
  找到占位符: ['${D3处置对策3}', '${D3状态3}', '${D3状态1}', '${D3范围3}', '${D3进度备注3}', '${D3进度备注1}', '${D3处置对策4}', '${D3处置对策2}', '${D3范围4}', '${D3完成期限4}', '${D3状态4}', '${D3状态2}', '${D3范围1}', '${D3责任人2}', '${D3完成期限3}', '${D3完成期限2}', '${D3进度备注2}', '${D3责任人3}', '${D3责任人4}', '${D3进度备注4}', '${D3完成期限1}', '${D3范围2}', '${D3责任人1}', '${D3处置对策1}']
表格 8 处理完成，无替换
处理表格 9:
表格大小: 8行 x 2列
处理第 5 页幻灯片...
处理第 6 页幻灯片...
处理第 7 页幻灯片...
  找到占位符: ['${D4判定5}', '${D4证据5}', '${D4证据1}', '${D4可能原因1}', '${D4原因小结}', '${D4可能原因4}', '${D4可能原因7}', '${D4判定2}', '${D4判定1}', '${D4判定3}', '${D4可能原因3}', '${D4可能原因5}', '${D4证据4}', '${D4可能原因2}', '${D4证据7}', '${D4判定7}', '${D4证据6}', '${D4判定6}', '${D4判定4}', '${D4证据3}', '${D4证据2}', '${D4可能原因6}']
表格 9 处理完成，无替换
处理表格 10:
表格大小: 8行 x 2列
处理第 8 页幻灯片...
  找到占位符: ['${D5责任人3}', '${D5纠正措施1}', '${D5计划完成日期2}', '${D5计划完成日期3}', '${D5责任人1}', '${D5责任人2}', '${D5责任人4}', '${D5纠正措施3}', '${D5计划完成日期4}', '${D5纠正措施2}', '${D5纠正措施4}', '${D5计划完成日期1}']
处理第 9 页幻灯片...
  找到占位符: ['${D6措施验证2}', '${D6验证结果2}', '${D6措施验证3}', '${D6验证人4}', '${D6验证时间4}', '${D6验证结果3}', '${D6验证结果4}', '${D6验证时间3}', '${D6验证人3}', '${D6验证结果1}', '${D6措施验证4}', '${D6验证时间1}', '${D6验证人1}', '${D6验证人2}', '${D6验证时间2}', '${D6措施验证1}']
表格 10 处理完成，无替换
处理表格 11:
表格大小: 8行 x 2列
处理第 10 页幻灯片...
  找到占位符: ['${D7计划完成日期3}', '${D7责任人1}', '${D7预防措施2}', '${D7预防措施3}', '${D7责任人3}', '${D7预防措施4}', '${D7计划完成日期1}', '${D7计划完成日期4}', '${D7责任人2}', '${D7计划完成日期2}', '${D7预防措施1}', '${D7责任人4}']
处理第 11 页幻灯片...
  找到占位符: ['${D8确认人}', '${D8有效性确认}', '${D8确认完成时间}']
处理第 12 页幻灯片...
PPT处理完成
表格 11 处理完成，无替换
处理表格 12:
表格大小: 5行 x 7列
处理单元格 [2,2]: '${D3范围1}'
    找到匹配: ${D3范围1} -> 工厂成品
    替换结果: '${D3范围1}' -> '工厂成品'
处理单元格 [2,3]: '${D3处置对策1}'
    找到匹配: ${D3处置对策1} -> 调换设备正/负探针组件在设备上的位置
    替换结果: '${D3处置对策1}' -> '调换设备正/负探针组件在设备上的位置'
处理单元格 [2,4]: '${D3责任人1}'
    找到匹配: ${D3责任人1} -> 武斌
    替换结果: '${D3责任人1}' -> '武斌'
处理单元格 [2,5]: '${D3完成期限1}'
    找到匹配: ${D3完成期限1} -> 2024/1/30
    替换结果: '${D3完成期限1}' -> '2024/1/30'
处理单元格 [2,6]: '${D3状态1}'
    找到匹配: ${D3状态1} -> 完成
    替换结果: '${D3状态1}' -> '完成'
处理单元格 [2,7]: '${D3进度备注1}'
    找到匹配: ${D3进度备注1} -> 由成玉峰执行调换，设备恢复运行
    替换结果: '${D3进度备注1}' -> '由成玉峰执行调换，设备恢复运行'
处理单元格 [3,2]: '${D3范围2}'
    删除未匹配的占位符: ['${D3范围2}']
    替换结果: '${D3范围2}' -> ''
处理单元格 [3,3]: '${D3处置对策2}'
    删除未匹配的占位符: ['${D3处置对策2}']
    替换结果: '${D3处置对策2}' -> ''
处理单元格 [3,4]: '${D3责任人2}'
    删除未匹配的占位符: ['${D3责任人2}']
    替换结果: '${D3责任人2}' -> ''
处理单元格 [3,5]: '${D3完成期限2}'
    删除未匹配的占位符: ['${D3完成期限2}']
    替换结果: '${D3完成期限2}' -> ''
处理单元格 [3,6]: '${D3状态2}'
    删除未匹配的占位符: ['${D3状态2}']
    替换结果: '${D3状态2}' -> ''
处理单元格 [3,7]: '${D3进度备注2}'
    删除未匹配的占位符: ['${D3进度备注2}']
    替换结果: '${D3进度备注2}' -> ''
处理单元格 [4,2]: '${D3范围3}'
    删除未匹配的占位符: ['${D3范围3}']
    替换结果: '${D3范围3}' -> ''
处理单元格 [4,3]: '${D3处置对策3}'
    删除未匹配的占位符: ['${D3处置对策3}']
    替换结果: '${D3处置对策3}' -> ''
处理单元格 [4,4]: '${D3责任人3}'
    删除未匹配的占位符: ['${D3责任人3}']
    替换结果: '${D3责任人3}' -> ''
处理单元格 [4,5]: '${D3完成期限3}'
    删除未匹配的占位符: ['${D3完成期限3}']
    替换结果: '${D3完成期限3}' -> ''
处理单元格 [4,6]: '${D3状态3}'
    删除未匹配的占位符: ['${D3状态3}']
    替换结果: '${D3状态3}' -> ''
处理单元格 [4,7]: '${D3进度备注3}'
    删除未匹配的占位符: ['${D3进度备注3}']
    替换结果: '${D3进度备注3}' -> ''
处理单元格 [5,2]: '${D3范围4}'
    删除未匹配的占位符: ['${D3范围4}']
    替换结果: '${D3范围4}' -> ''
处理单元格 [5,3]: '${D3处置对策4}'
    删除未匹配的占位符: ['${D3处置对策4}']
    替换结果: '${D3处置对策4}' -> ''
处理单元格 [5,4]: '${D3责任人4}'
    删除未匹配的占位符: ['${D3责任人4}']
    替换结果: '${D3责任人4}' -> ''
处理单元格 [5,5]: '${D3完成期限4}'
    删除未匹配的占位符: ['${D3完成期限4}']
    替换结果: '${D3完成期限4}' -> ''
处理单元格 [5,6]: '${D3状态4}'
    删除未匹配的占位符: ['${D3状态4}']
    替换结果: '${D3状态4}' -> ''
处理单元格 [5,7]: '${D3进度备注4}'
    删除未匹配的占位符: ['${D3进度备注4}']
    替换结果: '${D3进度备注4}' -> ''
表格 12 处理完成，有替换
处理表格 13:
表格大小: 5行 x 7列
表格 13 处理完成，无替换
处理表格 14:
表格大小: 5行 x 7列
表格 14 处理完成，无替换
处理表格 15:
表格大小: 5行 x 7列
表格 15 处理完成，无替换
处理表格 16:
表格大小: 5行 x 7列
表格 16 处理完成，无替换
处理表格 17:
表格大小: 6行 x 3列
处理单元格 [2,2]: '${D4why1}'
    找到匹配: ${D4why1} -> 为什么探针组件位置错误？
    替换结果: '${D4why1}' -> '为什么探针组件位置错误？'
处理单元格 [2,3]: '${D4answer1}'
    找到匹配: ${D4answer1} -> 装配时未按图纸要求操作
    替换结果: '${D4answer1}' -> '装配时未按图纸要求操作'
处理单元格 [3,2]: '${D4why2}'
    找到匹配: ${D4why2} -> 为什么未按图纸操作？
    替换结果: '${D4why2}' -> '为什么未按图纸操作？'
处理单元格 [3,3]: '${D4answer2}'
    找到匹配: ${D4answer2} -> SOP文件未明确左右向EL设备的探针装配位置
    替换结果: '${D4answer2}' -> 'SOP文件未明确左右向EL设备的探针装配位置'
处理单元格 [4,2]: '${D4why3}'
    找到匹配: ${D4why3} -> 为什么SOP未明确？
    替换结果: '${D4why3}' -> '为什么SOP未明确？'
处理单元格 [4,3]: '${D4answer3}'
    找到匹配: ${D4answer3} -> 研发与工艺部门未对接确认设备改造后的装配要求
    替换结果: '${D4answer3}' -> '研发与工艺部门未对接确认设备改造后的装配要求'
处理单元格 [5,2]: '${D4why4}'
    找到匹配: ${D4why4} -> 为什么未对接？
    替换结果: '${D4why4}' -> '为什么未对接？'
处理单元格 [5,3]: '${D4answer4}'
    找到匹配: ${D4answer4} -> 跨部门沟通流程缺失，未形成书面确认
    替换结果: '${D4answer4}' -> '跨部门沟通流程缺失，未形成书面确认'
处理单元格 [6,2]: '${D4why5}'
    找到匹配: ${D4why5} -> 为什么流程缺失？
    替换结果: '${D4why5}' -> '为什么流程缺失？'
处理单元格 [6,3]: '${D4answer5}'
    找到匹配: ${D4answer5} -> 缺乏针对设备改造的标准化协作机制
    替换结果: '${D4answer5}' -> '缺乏针对设备改造的标准化协作机制'
表格 17 处理完成，有替换
处理表格 18:
表格大小: 8行 x 4列
处理单元格 [2,2]: '${D4可能原因1}'
    找到匹配: ${D4可能原因1} -> 研发与工艺未对接确认装配位置
    替换结果: '${D4可能原因1}' -> '研发与工艺未对接确认装配位置'
处理单元格 [2,3]: '${D4判定1}'
    找到匹配: ${D4判定1} -> 主因
    替换结果: '${D4判定1}' -> '主因'
处理单元格 [2,4]: '${D4证据1}'
    找到匹配: ${D4证据1} -> 未发送装配确认邮件
    替换结果: '${D4证据1}' -> '未发送装配确认邮件'
处理单元格 [3,2]: '${D4可能原因2}'
    找到匹配: ${D4可能原因2} -> 缺乏装配SOP文件
    替换结果: '${D4可能原因2}' -> '缺乏装配SOP文件'
处理单元格 [3,3]: '${D4判定2}'
    找到匹配: ${D4判定2} -> 次因
    替换结果: '${D4判定2}' -> '次因'
处理单元格 [3,4]: '${D4证据2}'
    找到匹配: ${D4证据2} -> 无书面指导文件
    替换结果: '${D4证据2}' -> '无书面指导文件'
处理单元格 [4,2]: '${D4可能原因3}'
    找到匹配: ${D4可能原因3} -> 质检未进行装配位置确认
    替换结果: '${D4可能原因3}' -> '质检未进行装配位置确认'
处理单元格 [4,3]: '${D4判定3}'
    找到匹配: ${D4判定3} -> 次因
    替换结果: '${D4判定3}' -> '次因'
处理单元格 [4,4]: '${D4证据3}'
    找到匹配: ${D4证据3} -> 质检记录无相关检查项
    替换结果: '${D4证据3}' -> '质检记录无相关检查项'
处理单元格 [5,2]: '${D4可能原因4}'
    删除未匹配的占位符: ['${D4可能原因4}']
    替换结果: '${D4可能原因4}' -> ''
处理单元格 [5,3]: '${D4判定4}'
    删除未匹配的占位符: ['${D4判定4}']
    替换结果: '${D4判定4}' -> ''
处理单元格 [5,4]: '${D4证据4}'
    删除未匹配的占位符: ['${D4证据4}']
172.18.0.6 - - [19/Jun/2025 14:12:46] "POST /process_ppt HTTP/1.1" 200 -
    替换结果: '${D4证据4}' -> ''
处理单元格 [6,2]: '${D4可能原因5}'
    删除未匹配的占位符: ['${D4可能原因5}']
    替换结果: '${D4可能原因5}' -> ''
处理单元格 [6,3]: '${D4判定5}'
    删除未匹配的占位符: ['${D4判定5}']
    替换结果: '${D4判定5}' -> ''
处理单元格 [6,4]: '${D4证据5}'
    删除未匹配的占位符: ['${D4证据5}']
    替换结果: '${D4证据5}' -> ''
处理单元格 [7,2]: '${D4可能原因6}'
    删除未匹配的占位符: ['${D4可能原因6}']
    替换结果: '${D4可能原因6}' -> ''
处理单元格 [7,3]: '${D4判定6}'
    删除未匹配的占位符: ['${D4判定6}']
    替换结果: '${D4判定6}' -> ''
处理单元格 [7,4]: '${D4证据6}'
    删除未匹配的占位符: ['${D4证据6}']
    替换结果: '${D4证据6}' -> ''
处理单元格 [8,2]: '${D4可能原因7}'
    删除未匹配的占位符: ['${D4可能原因7}']
    替换结果: '${D4可能原因7}' -> ''
处理单元格 [8,3]: '${D4判定7}'
    删除未匹配的占位符: ['${D4判定7}']
    替换结果: '${D4判定7}' -> ''
处理单元格 [8,4]: '${D4证据7}'
    删除未匹配的占位符: ['${D4证据7}']
    替换结果: '${D4证据7}' -> ''
表格 18 处理完成，有替换
处理表格 19:
表格大小: 6行 x 3列
表格 19 处理完成，无替换
处理表格 20:
表格大小: 8行 x 4列
表格 20 处理完成，无替换
处理表格 21:
表格大小: 6行 x 3列
表格 21 处理完成，无替换
处理表格 22:
表格大小: 8行 x 4列
表格 22 处理完成，无替换
处理表格 23:
表格大小: 6行 x 3列
表格 23 处理完成，无替换
处理表格 24:
表格大小: 8行 x 4列
表格 24 处理完成，无替换
处理表格 25:
表格大小: 6行 x 3列
表格 25 处理完成，无替换
处理表格 26:
表格大小: 8行 x 4列
表格 26 处理完成，无替换
处理表格 27:
表格大小: 5行 x 4列
处理单元格 [2,2]: '${D5纠正措施1}'
    找到匹配: ${D5纠正措施1} -> 工艺部编制《探针组件装配SOP》文件并下发至各装配岗位
    替换结果: '${D5纠正措施1}' -> '工艺部编制《探针组件装配SOP》文件并下发至各装配岗位'
处理单元格 [2,3]: '${D5责任人1}'
    找到匹配: ${D5责任人1} -> 周淳
    替换结果: '${D5责任人1}' -> '周淳'
处理单元格 [2,4]: '${D5计划完成日期1}'
    找到匹配: ${D5计划完成日期1} -> 2024/2/5
    替换结果: '${D5计划完成日期1}' -> '2024/2/5'
处理单元格 [3,2]: '${D5纠正措施2}'
    删除未匹配的占位符: ['${D5纠正措施2}']
    替换结果: '${D5纠正措施2}' -> ''
处理单元格 [3,3]: '${D5责任人2}'
    删除未匹配的占位符: ['${D5责任人2}']
    替换结果: '${D5责任人2}' -> ''
处理单元格 [3,4]: '${D5计划完成日期2}'
    删除未匹配的占位符: ['${D5计划完成日期2}']
    替换结果: '${D5计划完成日期2}' -> ''
处理单元格 [4,2]: '${D5纠正措施3}'
    删除未匹配的占位符: ['${D5纠正措施3}']
    替换结果: '${D5纠正措施3}' -> ''
处理单元格 [4,3]: '${D5责任人3}'
    删除未匹配的占位符: ['${D5责任人3}']
    替换结果: '${D5责任人3}' -> ''
处理单元格 [4,4]: '${D5计划完成日期3}'
    删除未匹配的占位符: ['${D5计划完成日期3}']
    替换结果: '${D5计划完成日期3}' -> ''
处理单元格 [5,2]: '${D5纠正措施4}'
    删除未匹配的占位符: ['${D5纠正措施4}']
    替换结果: '${D5纠正措施4}' -> ''
处理单元格 [5,3]: '${D5责任人4}'
    删除未匹配的占位符: ['${D5责任人4}']
    替换结果: '${D5责任人4}' -> ''
处理单元格 [5,4]: '${D5计划完成日期4}'
    删除未匹配的占位符: ['${D5计划完成日期4}']
    替换结果: '${D5计划完成日期4}' -> ''
表格 27 处理完成，有替换
处理表格 28:
表格大小: 5行 x 4列
表格 28 处理完成，无替换
处理表格 29:
表格大小: 5行 x 4列
表格 29 处理完成，无替换
处理表格 30:
表格大小: 5行 x 4列
表格 30 处理完成，无替换
处理表格 31:
表格大小: 5行 x 4列
表格 31 处理完成，无替换
处理表格 32:
表格大小: 5行 x 5列
处理单元格 [2,2]: '${D6措施验证1}'
    找到匹配: ${D6措施验证1} -> 车间质检对照图纸和SOP文件对探针组件装配位置进行100%检验
    替换结果: '${D6措施验证1}' -> '车间质检对照图纸和SOP文件对探针组件装配位置进行100%检验'
处理单元格 [2,3]: '${D6验证人1}'
    找到匹配: ${D6验证人1} -> 孙宇航
    替换结果: '${D6验证人1}' -> '孙宇航'
处理单元格 [2,4]: '${D6验证时间1}'
    找到匹配: ${D6验证时间1} -> 2024/2/6
    替换结果: '${D6验证时间1}' -> '2024/2/6'
处理单元格 [2,5]: '${D6验证结果1}'
    找到匹配: ${D6验证结果1} -> 连续3台设备装配位置100%符合要求
    替换结果: '${D6验证结果1}' -> '连续3台设备装配位置100%符合要求'
处理单元格 [3,2]: '${D6措施验证2}'
    删除未匹配的占位符: ['${D6措施验证2}']
    替换结果: '${D6措施验证2}' -> ''
处理单元格 [3,3]: '${D6验证人2}'
    删除未匹配的占位符: ['${D6验证人2}']
    替换结果: '${D6验证人2}' -> ''
处理单元格 [3,4]: '${D6验证时间2}'
    删除未匹配的占位符: ['${D6验证时间2}']
    替换结果: '${D6验证时间2}' -> ''
处理单元格 [3,5]: '${D6验证结果2}'
    删除未匹配的占位符: ['${D6验证结果2}']
    替换结果: '${D6验证结果2}' -> ''
处理单元格 [4,2]: '${D6措施验证3}'
    删除未匹配的占位符: ['${D6措施验证3}']
    替换结果: '${D6措施验证3}' -> ''
处理单元格 [4,3]: '${D6验证人3}'
    删除未匹配的占位符: ['${D6验证人3}']
    替换结果: '${D6验证人3}' -> ''
处理单元格 [4,4]: '${D6验证时间3}'
    删除未匹配的占位符: ['${D6验证时间3}']
    替换结果: '${D6验证时间3}' -> ''
处理单元格 [4,5]: '${D6验证结果3}'
    删除未匹配的占位符: ['${D6验证结果3}']
    替换结果: '${D6验证结果3}' -> ''
处理单元格 [5,2]: '${D6措施验证4}'
    删除未匹配的占位符: ['${D6措施验证4}']
    替换结果: '${D6措施验证4}' -> ''
处理单元格 [5,3]: '${D6验证人4}'
    删除未匹配的占位符: ['${D6验证人4}']
    替换结果: '${D6验证人4}' -> ''
处理单元格 [5,4]: '${D6验证时间4}'
    删除未匹配的占位符: ['${D6验证时间4}']
    替换结果: '${D6验证时间4}' -> ''
处理单元格 [5,5]: '${D6验证结果4}'
    删除未匹配的占位符: ['${D6验证结果4}']
    替换结果: '${D6验证结果4}' -> ''
表格 32 处理完成，有替换
处理表格 33:
表格大小: 5行 x 5列
表格 33 处理完成，无替换
处理表格 34:
表格大小: 5行 x 5列
表格 34 处理完成，无替换
处理表格 35:
表格大小: 5行 x 5列
表格 35 处理完成，无替换
处理表格 36:
表格大小: 5行 x 5列
表格 36 处理完成，无替换
处理表格 37:
表格大小: 5行 x 4列
处理单元格 [2,2]: '${D7预防措施1}'
    找到匹配: ${D7预防措施1} -> 建立设备改造项目跨部门协作流程
    替换结果: '${D7预防措施1}' -> '建立设备改造项目跨部门协作流程'
处理单元格 [2,3]: '${D7责任人1}'
    找到匹配: ${D7责任人1} -> 武斌
    替换结果: '${D7责任人1}' -> '武斌'
处理单元格 [2,4]: '${D7计划完成日期1}'
    找到匹配: ${D7计划完成日期1} -> 2024/2/20
    替换结果: '${D7计划完成日期1}' -> '2024/2/20'
处理单元格 [3,2]: '${D7预防措施2}'
    删除未匹配的占位符: ['${D7预防措施2}']
    替换结果: '${D7预防措施2}' -> ''
处理单元格 [3,3]: '${D7责任人2}'
    删除未匹配的占位符: ['${D7责任人2}']
    替换结果: '${D7责任人2}' -> ''
处理单元格 [3,4]: '${D7计划完成日期2}'
    删除未匹配的占位符: ['${D7计划完成日期2}']
    替换结果: '${D7计划完成日期2}' -> ''
处理单元格 [4,2]: '${D7预防措施3}'
172.18.0.6 - - [19/Jun/2025 14:12:46] "POST /process_docx HTTP/1.1" 200 -
10.60.13.200 - - [19/Jun/2025 14:12:56] "GET /download/0f30dd35-2632-4cd5-98fe-6d4c628c1f2b HTTP/1.1" 200 -
10.60.13.200 - - [19/Jun/2025 14:12:58] "GET /download_file/0f30dd35-2632-4cd5-98fe-6d4c628c1f2b HTTP/1.1" 200 -
    删除未匹配的占位符: ['${D7预防措施3}']
    替换结果: '${D7预防措施3}' -> ''
处理单元格 [4,3]: '${D7责任人3}'
    删除未匹配的占位符: ['${D7责任人3}']
    替换结果: '${D7责任人3}' -> ''
处理单元格 [4,4]: '${D7计划完成日期3}'
    删除未匹配的占位符: ['${D7计划完成日期3}']
    替换结果: '${D7计划完成日期3}' -> ''
处理单元格 [5,2]: '${D7预防措施4}'
    删除未匹配的占位符: ['${D7预防措施4}']
    替换结果: '${D7预防措施4}' -> ''
处理单元格 [5,3]: '${D7责任人4}'
    删除未匹配的占位符: ['${D7责任人4}']
    替换结果: '${D7责任人4}' -> ''
处理单元格 [5,4]: '${D7计划完成日期4}'
    删除未匹配的占位符: ['${D7计划完成日期4}']
    替换结果: '${D7计划完成日期4}' -> ''
表格 37 处理完成，有替换
处理表格 38:
表格大小: 5行 x 4列
表格 38 处理完成，无替换
处理表格 39:
表格大小: 5行 x 4列
表格 39 处理完成，无替换
处理表格 40:
表格大小: 5行 x 4列
表格 40 处理完成，无替换
处理表格 41:
表格大小: 5行 x 4列
表格 41 处理完成，无替换
DOCX处理完成
文件已保存: generated_docx/20250619_141246_85e07e53.docx
扁平化后的替换数据: {'${D0标题}': '生产线A区产品尺寸超差问题汇报', '${D0汇报人}': '张三', '${D0汇报时间}': '2023年10月5日', '${D1组长姓名}': '李四', '${D1组长部门}': '生产部', '${D1组长职位}': '经理', '${D1组长主要职责}': '负责问题解决及协调', '${D1成员1姓名}': '张三', '${D1成员1部门}': '质检部', '${D1成员1职位}': '质检员', '${D1成员1主要职责}': '发现问题并报告', '${D1成员2姓名}': '王五', '${D1成员2部门}': '生产部', '${D1成员2职位}': '工程师', '${D1成员2主要职责}': '制定并实施永久措施', '${D1成员3姓名}': '赵六', '${D1成员3部门}': '质检部', '${D1成员3职位}': '主管', '${D1成员3主要职责}': '措施验证', '${D2事件整体描述}': '2023年10月5日，生产线A区质检员张三在常规检测中发现一批产品（型号X-200）尺寸超差，导致客户投诉。问题产品共50件，严重程度为高，直接影响交付计划。', '${D2何时发生}': '2023年10月5日', '${D2何地发生}': '生产线A区', '${D2何人发现}': '张三', '${D2为什么是这问题}': '测量设备校准过期导致数据偏差', '${D2发生了什么问题}': '产品尺寸超差', '${D2问题如何发生}': '测量设备校准过期导致数据偏差，生产线使用了错误的数据', '${D2问题影响程度}': '严重，直接影响交付计划', '${D3范围1}': '生产线A区', '${D3处置对策1}': '立即停用设备并重新校准，隔离不良品', '${D3责任人1}': '李四', '${D3完成期限1}': '2023年10月6日', '${D3状态1}': '已完成', '${D3进度备注1}': '经校准后设备检测合格，不良品返工后符合标准', '${D4why1}': '为什么测量设备校准过期？', '${D4answer1}': '因为设备维护人员没有按照规定时间进行校准', '${D4why2}': '为什么设备维护人员没有按照规定时间进行校准？', '${D4answer2}': '因为设备维护人员没有按照规定时间进行校准', '${D4why3}': '为什么设备维护人员没有按照规定时间进行校准？', '${D4answer3}': '因为设备维护人员没有按照规定时间进行校准', '${D4why4}': '为什么设备维护人员没有按照规定时间进行校准？', '${D4answer4}': '因为设备维护人员没有按照规定时间进行校准', '${D4why5}': '为什么设备维护人员没有按照规定时间进行校准？', '${D4answer5}': '因为设备维护人员没有按照规定时间进行校准', '${D4人原因1}': '人', '${D4人原因2}': '人', '${D4人原因3}': '人', '${D4机原因1}': '机', '${D4机原因2}': '机', '${D4机原因3}': '机', '${D4料原因1}': '料', '${D4料原因2}': '料', '${D4料原因3}': '料', '${D4法原因1}': '法', '${D4法原因2}': '法', '${D4法原因3}': '法', '${D4环原因1}': '环', '${D4环原因2}': '环', '${D4环原因3}': '环', '${D4测原因1}': '测', '${D4测原因2}': '测', '${D4测原因3}': '测', '${D4可能原因1}': '测量设备校准过期导致数据偏差', '${D4判定1}': '是', '${D4证据1}': '测量设备校准记录显示最后一次校准日期已过期', '${D4可能原因2}': '设备维护流程未定期检查校准状态', '${D4判定2}': '是', '${D4证据2}': '维护记录中未发现定期检查校准状态的记录', '${D4可能原因3}': '缺乏设备校准周期管理', '${D4判定3}': '是', '${D4证据3}': '没有设备校准周期管理表', '${D4原因小结}': '设备校准管理制度执行不到位，导致测量设备校准过期，数据偏差，最终产生质量问题', '${D5纠正措施1}': '建立设备校准周期管理表，明确责任人及校准频率，并纳入SOP', '${D5责任人1}': '王五', '${D5计划完成日期1}': '2023年11月1日', '${D6措施验证1}': '通过连续3批次产品抽样检测（每批次10件），确认尺寸合格率100%', '${D6验证人1}': '赵六', '${D6验证时间1}': '2023年11月15日', '${D6验证结果1}': '验证通过，产品尺寸符合标准', '${D7预防措施1}': '更新《设备维护管理制度》，新增校准到期预警流程，并开发自动提醒系统', '${D7责任人1}': '钱七', '${D7计划完成日期1}': '2023年12月1日', '${D8有效性确认}': '确认通过', '${D8确认人}': '赵六', '${D8确认完成时间}': '2023年11月15日'}
开始处理PPT中的所有占位符...
处理第 1 页幻灯片...
  找到占位符: ['${D0汇报时间}', '${D0汇报人}', '${D0标题}']
处理第 2 页幻灯片...
  找到占位符: ['${D1成员5主要职责}', '${D1成员6部门}', '${D1成员5姓名}', '${D1成员6主要职责}', '${D1成员2部门}', '${D1成员1姓名}', '${D1成员4姓名}', '${D1成员3部门}', '${D1成员6职位}', '${D1成员2主要职责}', '${D1成员4部门}', '${D1成员1主要职责}', '${D1成员3姓名}', '${D1成员1职位}', '${D1成员4主要职责}', '${D1成员6姓名}', '${D1成员4职位}', '${D1成员5职位}', '${D1组长部门}', '${D1成员1部门}', '${D1组长主要职责}', '${D1组长职位}', '${D1成员3职位}', '${D1成员2职位}', '${D1成员5部门}', '${D1组长姓名}', '${D1成员2姓名}', '${D1成员3主要职责}']
处理第 3 页幻灯片...
  找到占位符: ['${D2何时发生}', '${D2何地发生}', '${D2问题如何发生}', '${D2发生了什么问题}', '${D2何人发现}', '${D2问题影响程度}', '${D2事件整体描述}', '${D2为什么是这问题}']
处理第 4 页幻灯片...
  找到占位符: ['${D3处置对策3}', '${D3状态3}', '${D3状态1}', '${D3范围3}', '${D3进度备注3}', '${D3进度备注1}', '${D3处置对策4}', '${D3处置对策2}', '${D3范围4}', '${D3完成期限4}', '${D3状态4}', '${D3状态2}', '${D3范围1}', '${D3责任人2}', '${D3完成期限3}', '${D3完成期限2}', '${D3进度备注2}', '${D3责任人3}', '${D3责任人4}', '${D3进度备注4}', '${D3完成期限1}', '${D3范围2}', '${D3责任人1}', '${D3处置对策1}']
处理第 5 页幻灯片...
处理第 6 页幻灯片...
处理第 7 页幻灯片...
  找到占位符: ['${D4判定5}', '${D4证据5}', '${D4证据1}', '${D4可能原因1}', '${D4原因小结}', '${D4可能原因4}', '${D4可能原因7}', '${D4判定2}', '${D4判定1}', '${D4判定3}', '${D4可能原因3}', '${D4可能原因5}', '${D4证据4}', '${D4可能原因2}', '${D4证据7}', '${D4判定7}', '${D4证据6}', '${D4判定6}', '${D4判定4}', '${D4证据3}', '${D4证据2}', '${D4可能原因6}']
处理第 8 页幻灯片...
  找到占位符: ['${D5责任人3}', '${D5纠正措施1}', '${D5计划完成日期2}', '${D5计划完成日期3}', '${D5责任人1}', '${D5责任人2}', '${D5责任人4}', '${D5纠正措施3}', '${D5计划完成日期4}', '${D5纠正措施2}', '${D5纠正措施4}', '${D5计划完成日期1}']
处理第 9 页幻灯片...
127.0.0.1 - - [19/Jun/2025 14:24:45] "POST /process_ppt HTTP/1.1" 200 -
127.0.0.1 - - [19/Jun/2025 14:24:48] "GET / HTTP/1.1" 404 -
127.0.0.1 - - [19/Jun/2025 14:24:48] "GET /favicon.ico HTTP/1.1" 404 -
127.0.0.1 - - [19/Jun/2025 14:25:06] "GET /download/752dee90-bbb3-47a2-b38f-668e5a3a599d HTTP/1.1" 200 -
127.0.0.1 - - [19/Jun/2025 14:25:08] "GET /download_file/752dee90-bbb3-47a2-b38f-668e5a3a599d HTTP/1.1" 200 -
  找到占位符: ['${D6措施验证2}', '${D6验证结果2}', '${D6措施验证3}', '${D6验证人4}', '${D6验证时间4}', '${D6验证结果3}', '${D6验证结果4}', '${D6验证时间3}', '${D6验证人3}', '${D6验证结果1}', '${D6措施验证4}', '${D6验证时间1}', '${D6验证人1}', '${D6验证人2}', '${D6验证时间2}', '${D6措施验证1}']
处理第 10 页幻灯片...
  找到占位符: ['${D7计划完成日期3}', '${D7责任人1}', '${D7预防措施2}', '${D7预防措施3}', '${D7责任人3}', '${D7预防措施4}', '${D7计划完成日期1}', '${D7计划完成日期4}', '${D7责任人2}', '${D7计划完成日期2}', '${D7预防措施1}', '${D7责任人4}']
处理第 11 页幻灯片...
  找到占位符: ['${D8确认人}', '${D8有效性确认}', '${D8确认完成时间}']
处理第 12 页幻灯片...
PPT处理完成
扁平化后的替换数据: {'${D0标题}': '生产线A区产品尺寸超差问题汇报', '${D0汇报人}': '张三', '${D0汇报时间}': '2023年10月5日', '${D1组长姓名}': '李四', '${D1组长部门}': '生产部', '${D1组长职位}': '经理', '${D1组长主要职责}': '负责问题解决及协调', '${D1成员1姓名}': '张三', '${D1成员1部门}': '质检部', '${D1成员1职位}': '质检员', '${D1成员1主要职责}': '发现问题并报告', '${D1成员2姓名}': '王五', '${D1成员2部门}': '生产部', '${D1成员2职位}': '工程师', '${D1成员2主要职责}': '制定并实施永久措施', '${D1成员3姓名}': '赵六', '${D1成员3部门}': '质检部', '${D1成员3职位}': '主管', '${D1成员3主要职责}': '措施验证', '${D2事件整体描述}': '2023年10月5日，生产线A区质检员张三在常规检测中发现一批产品（型号X-200）尺寸超差，导致客户投诉。问题产品共50件，严重程度为高，直接影响交付计划。', '${D2何时发生}': '2023年10月5日', '${D2何地发生}': '生产线A区', '${D2何人发现}': '张三', '${D2为什么是这问题}': '测量设备校准过期导致数据偏差', '${D2发生了什么问题}': '产品尺寸超差', '${D2问题如何发生}': '测量设备校准过期导致数据偏差，生产线使用了错误的数据', '${D2问题影响程度}': '严重，直接影响交付计划', '${D3范围1}': '生产线A区', '${D3处置对策1}': '立即停用设备并重新校准，隔离不良品', '${D3责任人1}': '李四', '${D3完成期限1}': '2023年10月6日', '${D3状态1}': '已完成', '${D3进度备注1}': '经校准后设备检测合格，不良品返工后符合标准', '${D4why1}': '为什么测量设备校准过期？', '${D4answer1}': '因为设备维护人员没有按照规定时间进行校准', '${D4why2}': '为什么设备维护人员没有按照规定时间进行校准？', '${D4answer2}': '因为设备维护人员没有按照规定时间进行校准', '${D4why3}': '为什么设备维护人员没有按照规定时间进行校准？', '${D4answer3}': '因为设备维护人员没有按照规定时间进行校准', '${D4why4}': '为什么设备维护人员没有按照规定时间进行校准？', '${D4answer4}': '因为设备维护人员没有按照规定时间进行校准', '${D4why5}': '为什么设备维护人员没有按照规定时间进行校准？', '${D4answer5}': '因为设备维护人员没有按照规定时间进行校准', '${D4人原因1}': '人', '${D4人原因2}': '人', '${D4人原因3}': '人', '${D4机原因1}': '机', '${D4机原因2}': '机', '${D4机原因3}': '机', '${D4料原因1}': '料', '${D4料原因2}': '料', '${D4料原因3}': '料', '${D4法原因1}': '法', '${D4法原因2}': '法', '${D4法原因3}': '法', '${D4环原因1}': '环', '${D4环原因2}': '环', '${D4环原因3}': '环', '${D4测原因1}': '测', '${D4测原因2}': '测', '${D4测原因3}': '测', '${D4可能原因1}': '测量设备校准过期导致数据偏差', '${D4判定1}': '是', '${D4证据1}': '测量设备校准记录显示最后一次校准日期已过期', '${D4可能原因2}': '设备维护流程未定期检查校准状态', '${D4判定2}': '是', '${D4证据2}': '维护记录中未发现定期检查校准状态的记录', '${D4可能原因3}': '缺乏设备校准周期管理', '${D4判定3}': '是', '${D4证据3}': '没有设备校准周期管理表', '${D4原因小结}': '设备校准管理制度执行不到位，导致测量设备校准过期，数据偏差，最终产生质量问题', '${D5纠正措施1}': '建立设备校准周期管理表，明确责任人及校准频率，并纳入SOP', '${D5责任人1}': '王五', '${D5计划完成日期1}': '2023年11月1日', '${D6措施验证1}': '通过连续3批次产品抽样检测（每批次10件），确认尺寸合格率100%', '${D6验证人1}': '赵六', '${D6验证时间1}': '2023年11月15日', '${D6验证结果1}': '验证通过，产品尺寸符合标准', '${D7预防措施1}': '更新《设备维护管理制度》，新增校准到期预警流程，并开发自动提醒系统', '${D7责任人1}': '钱七', '${D7计划完成日期1}': '2023年12月1日', '${D8有效性确认}': '确认通过', '${D8确认人}': '赵六', '${D8确认完成时间}': '2023年11月15日'}
开始处理PPT中的所有占位符...
处理第 1 页幻灯片...
  找到占位符: ['${D0汇报时间}', '${D0汇报人}', '${D0标题}']
处理第 2 页幻灯片...
  找到占位符: ['${D1成员5主要职责}', '${D1成员6部门}', '${D1成员5姓名}', '${D1成员6主要职责}', '${D1成员2部门}', '${D1成员1姓名}', '${D1成员4姓名}', '${D1成员3部门}', '${D1成员6职位}', '${D1成员2主要职责}', '${D1成员4部门}', '${D1成员1主要职责}', '${D1成员3姓名}', '${D1成员1职位}', '${D1成员4主要职责}', '${D1成员6姓名}', '${D1成员4职位}', '${D1成员5职位}', '${D1组长部门}', '${D1成员1部门}', '${D1组长主要职责}', '${D1组长职位}', '${D1成员3职位}', '${D1成员2职位}', '${D1成员5部门}', '${D1组长姓名}', '${D1成员2姓名}', '${D1成员3主要职责}']
处理第 3 页幻灯片...
  找到占位符: ['${D2何时发生}', '${D2何地发生}', '${D2问题如何发生}', '${D2发生了什么问题}', '${D2何人发现}', '${D2问题影响程度}', '${D2事件整体描述}', '${D2为什么是这问题}']
处理第 4 页幻灯片...
  找到占位符: ['${D3处置对策3}', '${D3状态3}', '${D3状态1}', '${D3范围3}', '${D3进度备注3}', '${D3进度备注1}', '${D3处置对策4}', '${D3处置对策2}', '${D3范围4}', '${D3完成期限4}', '${D3状态4}', '${D3状态2}', '${D3范围1}', '${D3责任人2}', '${D3完成期限3}', '${D3完成期限2}', '${D3进度备注2}', '${D3责任人3}', '${D3责任人4}', '${D3进度备注4}', '${D3完成期限1}', '${D3范围2}', '${D3责任人1}', '${D3处置对策1}']
处理第 5 页幻灯片...
处理第 6 页幻灯片...
处理第 7 页幻灯片...
  找到占位符: ['${D4判定5}', '${D4证据5}', '${D4证据1}', '${D4可能原因1}', '${D4原因小结}', '${D4可能原因4}', '${D4可能原因7}', '${D4判定2}', '${D4判定1}', '${D4判定3}', '${D4可能原因3}', '${D4可能原因5}', '${D4证据4}', '${D4可能原因2}', '${D4证据7}', '${D4判定7}', '${D4证据6}', '${D4判定6}', '${D4判定4}', '${D4证据3}', '${D4证据2}', '${D4可能原因6}']
处理第 8 页幻灯片...
  找到占位符: ['${D5责任人3}', '${D5纠正措施1}', '${D5计划完成日期2}', '${D5计划完成日期3}', '${D5责任人1}', '${D5责任人2}', '${D5责任人4}', '${D5纠正措施3}', '${D5计划完成日期4}', '${D5纠正措施2}', '${D5纠正措施4}', '${D5计划完成日期1}']
处理第 9 页幻灯片...
  找到占位符: ['${D6措施验证2}', '${D6验证结果2}', '${D6措施验证3}', '${D6验证人4}', '${D6验证时间4}', '${D6验证结果3}', '${D6验证结果4}', '${D6验证时间3}', '${D6验证人3}', '${D6验证结果1}', '${D6措施验证4}', '${D6验证时间1}', '${D6验证人1}', '${D6验证人2}', '${D6验证时间2}', '${D6措施验证1}']
处理第 10 页幻灯片...
127.0.0.1 - - [19/Jun/2025 14:37:48] "POST /process_ppt HTTP/1.1" 200 -
127.0.0.1 - - [19/Jun/2025 14:37:53] "GET /download/93be678f-3df1-43cf-bc2d-95e785e8e868 HTTP/1.1" 200 -
127.0.0.1 - - [19/Jun/2025 14:37:55] "GET /download_file/93be678f-3df1-43cf-bc2d-95e785e8e868 HTTP/1.1" 200 -
127.0.0.1 - - [19/Jun/2025 14:47:38] "POST /process_ppt HTTP/1.1" 200 -
127.0.0.1 - - [19/Jun/2025 14:47:41] "GET /download/81b6923a-df88-45c2-9ba7-5974884cadb4 HTTP/1.1" 200 -
127.0.0.1 - - [19/Jun/2025 14:47:43] "GET /download_file/81b6923a-df88-45c2-9ba7-5974884cadb4 HTTP/1.1" 200 -
