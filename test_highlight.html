<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>右侧导航栏高亮测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 20px;
        }
        
        .content-area {
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section {
            margin-bottom: 100px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .section h2 {
            color: #333;
            margin-top: 0;
        }
        
        /* 右侧导航栏样式 */
        .right-navigation {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            z-index: 1000;
            overflow: visible;
            transition: all 0.3s ease;
            width: 70px;
            padding: 12px 0;
        }
        
        .nav-sections {
            padding: 0;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .nav-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 6px;
            cursor: pointer;
            transition: all 0.2s;
            margin: 2px 8px;
            border-radius: 10px;
            position: relative;
        }
        
        .nav-section:hover {
            background: #f0f4ff;
            transform: translateY(-1px);
        }
        
        .nav-section.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.3);
            animation: activePulse 2s ease-in-out infinite alternate;
        }
        
        @keyframes activePulse {
            0% {
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            }
            100% {
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            }
        }
        
        .section-label {
            font-size: 12px;
            font-weight: 600;
            color: #667eea;
            text-align: center;
            margin-bottom: 2px;
        }
        
        .nav-section.active .section-label {
            color: white;
        }
        
        .section-progress {
            font-size: 9px;
            color: #6c757d;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.9);
            padding: 1px 4px;
            border-radius: 6px;
            min-width: 16px;
            text-align: center;
        }
        
        .nav-section.active .section-progress {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .test-buttons {
            margin-bottom: 20px;
        }
        
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        .test-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="content-area">
            <h1>右侧导航栏高亮测试</h1>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="setActiveSection('guide')">激活 Guide</button>
                <button class="test-btn" onclick="setActiveSection('d0')">激活 D0</button>
                <button class="test-btn" onclick="setActiveSection('d1')">激活 D1</button>
                <button class="test-btn" onclick="setActiveSection('d2')">激活 D2</button>
            </div>
            
            <div class="section" id="section-guide" data-section="guide">
                <h2>📝 修改意见</h2>
                <p>这是修改意见章节的内容...</p>
            </div>
            
            <div class="section" id="section-d0" data-section="d0">
                <h2>D0 基本信息</h2>
                <p>这是D0章节的内容...</p>
            </div>
            
            <div class="section" id="section-d1" data-section="d1">
                <h2>D1 建立小组</h2>
                <p>这是D1章节的内容...</p>
            </div>
            
            <div class="section" id="section-d2" data-section="d2">
                <h2>D2 问题说明</h2>
                <p>这是D2章节的内容...</p>
            </div>
        </div>
    </div>
    
    <!-- 右侧章节导航 -->
    <div class="right-navigation" id="right-navigation">
        <div class="nav-sections" id="nav-sections">
            <div class="nav-section active" data-section="guide">
                <div class="section-label">📝</div>
                <div class="section-progress" id="nav-progress-guide">-</div>
            </div>
            <div class="nav-section" data-section="d0">
                <div class="section-label">D0</div>
                <div class="section-progress" id="nav-progress-d0">0/4</div>
            </div>
            <div class="nav-section" data-section="d1">
                <div class="section-label">D1</div>
                <div class="section-progress" id="nav-progress-d1">0/12</div>
            </div>
            <div class="nav-section" data-section="d2">
                <div class="section-label">D2</div>
                <div class="section-progress" id="nav-progress-d2">0/8</div>
            </div>
        </div>
    </div>
    
    <script>
        let currentActiveSection = 'guide';
        
        // 设置活动章节
        function setActiveSection(sectionId) {
            console.log('设置活动章节:', sectionId);
            
            if (currentActiveSection === sectionId) return;
            
            currentActiveSection = sectionId;
            
            // 清除右侧导航栏的活动状态
            document.querySelectorAll('.nav-section').forEach(section => {
                section.classList.remove('active');
                console.log('清除活动状态:', section.dataset.section);
            });
            
            // 设置当前活动章节的高亮
            const activeNavSection = document.querySelector(`.nav-section[data-section="${sectionId}"]`);
            
            if (activeNavSection) {
                activeNavSection.classList.add('active');
                console.log('设置活动状态:', sectionId);
            } else {
                console.error('未找到对应的导航项:', sectionId);
            }
        }
        
        // 绑定导航点击事件
        document.querySelectorAll('.nav-section').forEach(section => {
            section.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const sectionId = this.dataset.section;
                console.log('点击导航项:', sectionId);
                setActiveSection(sectionId);
            });
        });
        
        // 滚动监听
        function initScrollTracking() {
            window.addEventListener('scroll', function() {
                const sections = document.querySelectorAll('.section[data-section]');
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const windowHeight = window.innerHeight;
                
                let bestSection = 'guide';
                let minDistance = Infinity;
                
                sections.forEach(section => {
                    const sectionId = section.dataset.section;
                    const sectionTop = section.offsetTop;
                    const sectionBottom = sectionTop + section.offsetHeight;
                    
                    // 如果章节在视口范围内
                    if (sectionTop <= scrollTop + windowHeight && sectionBottom >= scrollTop) {
                        const sectionCenter = (sectionTop + sectionBottom) / 2;
                        const viewportCenter = scrollTop + windowHeight / 2;
                        const distance = Math.abs(sectionCenter - viewportCenter);
                        
                        if (distance < minDistance) {
                            minDistance = distance;
                            bestSection = sectionId;
                        }
                    }
                });
                
                if (bestSection !== currentActiveSection) {
                    setActiveSection(bestSection);
                }
            }, { passive: true });
        }
        
        // 初始化
        initScrollTracking();
        console.log('测试页面已加载');
    </script>
</body>
</html> 