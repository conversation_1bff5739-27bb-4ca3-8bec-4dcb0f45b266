#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能PPT/DOCX处理使用示例
展示如何使用新的智能处理功能
"""

import json
import os
from datetime import datetime

# 示例：最小数据场景（空白内容多）
MINIMAL_DATA_EXAMPLE = {
    "D0汇报信息": {
        "${D0标题}": "生产线设备故障问题",
        "${D0汇报人}": "张工程师",
        "${D0汇报时间}": "2025-01-23",
        "${D0项目背景}": "生产线A区设备突发故障，影响正常生产"
    },
    "D1建立小组": {
        "组长": {
            "${D1组长姓名}": "李经理",
            "${D1组长部门}": "生产部",
            "${D1组长职位}": "生产经理",
            "${D1组长主要职责}": "负责问题解决协调"
        }
        # 注意：只有组长，没有成员 - 智能系统会清理空白成员行
    },
    "D2问题描述": {
        "${D2事件整体描述}": "2025年1月23日上午10点，生产线A区主要设备突然停机",
        "5W2H": {
            "${D2何时发生}": "2025-01-23 10:00",
            "${D2何地发生}": "生产线A区",
            "${D2何人发现}": "操作员王师傅",
            "${D2为什么是这问题}": "设备老化导致关键部件失效",
            "${D2发生了什么问题}": "主轴承损坏，设备无法正常运转",
            "${D2问题如何发生}": "长期高负荷运转，缺乏预防性维护",
            "${D2问题影响程度}": "严重，导致生产线停产4小时"
        }
    },
    "D3临时措施": {
        # 完全空白 - 智能系统会隐藏这个section
    },
    "D4根本原因": {
        "${D4原因小结}": "设备预防性维护不到位，缺乏有效的设备状态监控"
    },
    "D5永久措施": {
        # 完全空白 - 智能系统会隐藏这个section
    },
    "D6措施验证": {
        # 完全空白 - 智能系统会隐藏这个section
    },
    "D7预防措施": {
        # 完全空白 - 智能系统会隐藏这个section
    },
    "D8庆贺团队": {
        "${D8有效性确认}": "问题已临时解决，设备恢复正常运行",
        "${D8确认人}": "李经理",
        "${D8确认完成时间}": "2025-01-23"
    }
}

# 示例：溢出数据场景（内容过多）
OVERFLOW_DATA_EXAMPLE = {
    "D0汇报信息": {
        "${D0标题}": "供应链质量管理系统性问题",
        "${D0汇报人}": "质量总监",
        "${D0汇报时间}": "2025-01-23",
        "${D0项目背景}": "多个供应商同时出现质量问题，影响整个供应链稳定性"
    },
    "D1建立小组": {
        "组长": {
            "${D1组长姓名}": "王总监",
            "${D1组长部门}": "质量管理部",
            "${D1组长职位}": "质量总监",
            "${D1组长主要职责}": "统筹整个问题解决流程"
        },
        "成员1": {
            "${D1成员1姓名}": "张经理",
            "${D1成员1部门}": "采购部",
            "${D1成员1职位}": "采购经理",
            "${D1成员1主要职责}": "供应商管理和协调"
        },
        "成员2": {
            "${D1成员2姓名}": "李工程师",
            "${D1成员2部门}": "质量工程部",
            "${D1成员2职位}": "质量工程师",
            "${D1成员2主要职责}": "质量分析和改进"
        },
        "成员3": {
            "${D1成员3姓名}": "赵主管",
            "${D1成员3部门}": "生产部",
            "${D1成员3职位}": "生产主管",
            "${D1成员3主要职责}": "生产过程控制"
        },
        "成员4": {
            "${D1成员4姓名}": "钱分析师",
            "${D1成员4部门}": "财务部",
            "${D1成员4职位}": "成本分析师",
            "${D1成员4主要职责}": "成本影响分析"
        },
        "成员5": {
            "${D1成员5姓名}": "孙经理",
            "${D1成员5部门}": "销售部",
            "${D1成员5职位}": "销售经理",
            "${D1成员5主要职责}": "客户沟通协调"
        },
        "成员6": {
            "${D1成员6姓名}": "周工程师",
            "${D1成员6部门}": "研发部",
            "${D1成员6职位}": "研发工程师",
            "${D1成员6主要职责}": "技术改进支持"
        },
        "成员7": {
            "${D1成员7姓名}": "吴主管",
            "${D1成员7部门}": "物流部",
            "${D1成员7职位}": "物流主管",
            "${D1成员7主要职责}": "物流协调管理"
        },
        "成员8": {
            "${D1成员8姓名}": "郑专员",
            "${D1成员8部门}": "法务部",
            "${D1成员8职位}": "法务专员",
            "${D1成员8主要职责}": "合同和法律风险管理"
        }
        # 9个成员，远超过6个限制 - 智能系统会创建额外页面
    },
    "D3临时措施": {
        "临时措施1": {
            "${D3范围1}": "供应商A",
            "${D3处置对策1}": "立即暂停供货，要求提供质量改进计划",
            "${D3责任人1}": "张经理",
            "${D3完成期限1}": "2025-01-25",
            "${D3状态1}": "进行中",
            "${D3进度备注1}": "已通知供应商，等待改进计划"
        },
        "临时措施2": {
            "${D3范围2}": "供应商B",
            "${D3处置对策2}": "加强来料检验，100%全检",
            "${D3责任人2}": "李工程师",
            "${D3完成期限2}": "2025-01-24",
            "${D3状态2}": "已完成",
            "${D3进度备注2}": "已实施全检，发现问题率15%"
        },
        "临时措施3": {
            "${D3范围3}": "供应商C",
            "${D3处置对策3}": "派驻质量工程师现场指导",
            "${D3责任人3}": "李工程师",
            "${D3完成期限3}": "2025-01-26",
            "${D3状态3}": "计划中",
            "${D3进度备注3}": "正在安排人员出差"
        },
        "临时措施4": {
            "${D3范围4}": "内部生产",
            "${D3处置对策4}": "启动备用供应商，确保生产连续性",
            "${D3责任人4}": "赵主管",
            "${D3完成期限4}": "2025-01-24",
            "${D3状态4}": "已完成",
            "${D3进度备注4}": "备用供应商已激活，供货正常"
        },
        "临时措施5": {
            "${D3范围5}": "客户沟通",
            "${D3处置对策5}": "主动通知客户，说明质量保证措施",
            "${D3责任人5}": "孙经理",
            "${D3完成期限5}": "2025-01-25",
            "${D3状态5}": "进行中",
            "${D3进度备注5}": "已联系主要客户，反馈良好"
        },
        "临时措施6": {
            "${D3范围6}": "库存管理",
            "${D3处置对策6}": "隔离问题批次，防止问题产品流出",
            "${D3责任人6}": "吴主管",
            "${D3完成期限6}": "2025-01-24",
            "${D3状态6}": "已完成",
            "${D3进度备注6}": "问题批次已全部隔离标识"
        },
        "临时措施7": {
            "${D3范围7}": "质量体系",
            "${D3处置对策7}": "紧急修订供应商质量协议",
            "${D3责任人7}": "郑专员",
            "${D3完成期限7}": "2025-01-27",
            "${D3状态7}": "进行中",
            "${D3进度备注7}": "正在起草新的质量条款"
        }
        # 7个临时措施，超过5个限制 - 智能系统会创建额外页面
    },
    "D5永久措施": {
        "措施1": {
            "${D5纠正措施1}": "建立供应商分级管理体系，实施差异化管理策略",
            "${D5责任人1}": "张经理",
            "${D5计划完成日期1}": "2025-03-01"
        },
        "措施2": {
            "${D5纠正措施2}": "实施供应商质量能力评估，建立准入退出机制",
            "${D5责任人2}": "李工程师",
            "${D5计划完成日期2}": "2025-02-15"
        },
        "措施3": {
            "${D5纠正措施3}": "建立供应商质量数据平台，实现实时监控",
            "${D5责任人3}": "周工程师",
            "${D5计划完成日期3}": "2025-04-01"
        },
        "措施4": {
            "${D5纠正措施4}": "完善来料检验流程，增加关键控制点",
            "${D5责任人4}": "李工程师",
            "${D5计划完成日期4}": "2025-02-20"
        },
        "措施5": {
            "${D5纠正措施5}": "建立供应商质量问题快速响应机制",
            "${D5责任人5}": "张经理",
            "${D5计划完成日期5}": "2025-02-10"
        },
        "措施6": {
            "${D5纠正措施6}": "实施供应商现场质量审核制度",
            "${D5责任人6}": "王总监",
            "${D5计划完成日期6}": "2025-03-15"
        },
        "措施7": {
            "${D5纠正措施7}": "建立供应商质量培训体系",
            "${D5责任人7}": "李工程师",
            "${D5计划完成日期7}": "2025-03-30"
        }
        # 7个永久措施，超过5个限制 - 智能系统会创建额外页面
    }
}

def demonstrate_smart_processing():
    """演示智能处理功能"""
    print("🚀 智能PPT/DOCX处理功能演示")
    print("="*60)
    
    # 演示数据分析
    print("\n📊 场景1：最小数据分析")
    print("-" * 30)
    
    # 模拟数据分析过程
    minimal_sections_with_content = []
    minimal_sections_empty = []
    
    for section_key, section_data in MINIMAL_DATA_EXAMPLE.items():
        if isinstance(section_data, dict) and section_data:
            # 检查是否有实际内容
            has_content = any(v and str(v).strip() for v in section_data.values() if isinstance(v, str))
            if not has_content:
                # 检查嵌套内容
                has_nested_content = False
                for nested_key, nested_value in section_data.items():
                    if isinstance(nested_value, dict) and nested_value:
                        if any(v and str(v).strip() for v in nested_value.values() if isinstance(v, str)):
                            has_nested_content = True
                            break
                has_content = has_nested_content
            
            if has_content:
                minimal_sections_with_content.append(section_key)
            else:
                minimal_sections_empty.append(section_key)
    
    print(f"有内容的sections: {minimal_sections_with_content}")
    print(f"空白的sections: {minimal_sections_empty}")
    print(f"智能处理建议: 隐藏 {len(minimal_sections_empty)} 个空白sections")
    
    # 演示溢出检测
    print("\n📊 场景2：溢出数据分析")
    print("-" * 30)
    
    section_limits = {
        'D1建立小组': 6,
        'D3临时措施': 5,
        'D5永久措施': 5,
        'D6措施验证': 5,
        'D7预防措施': 5
    }
    
    overflow_detected = {}
    for section_key, section_data in OVERFLOW_DATA_EXAMPLE.items():
        if section_key in section_limits and isinstance(section_data, dict):
            content_count = 0
            for key, value in section_data.items():
                if isinstance(value, dict) and value:
                    # 检查是否有实际内容
                    if any(v and str(v).strip() for v in value.values() if isinstance(v, str)):
                        content_count += 1
            
            if content_count > section_limits[section_key]:
                overflow_detected[section_key] = {
                    'current_count': content_count,
                    'limit': section_limits[section_key],
                    'overflow_count': content_count - section_limits[section_key]
                }
    
    print(f"检测到溢出的sections:")
    total_overflow = 0
    for section, info in overflow_detected.items():
        print(f"  - {section}: {info['current_count']}项 (限制{info['limit']}项, 溢出{info['overflow_count']}项)")
        total_overflow += info['overflow_count']
    
    print(f"智能处理建议: 创建 {len(overflow_detected)} 个额外页面，容纳 {total_overflow} 项溢出内容")
    
    # 演示处理效果
    print("\n✨ 智能处理效果对比")
    print("-" * 30)
    
    print("传统方案:")
    print("  ❌ 最小数据场景: 生成大量空白表格行和空白sections")
    print("  ❌ 溢出数据场景: 内容被截断，信息丢失")
    print("  ❌ 用户体验: 文档冗余或不完整")
    
    print("\n智能方案:")
    print("  ✅ 最小数据场景: 自动隐藏空白内容，生成简洁文档")
    print("  ✅ 溢出数据场景: 自动创建额外页面，完整展示所有内容")
    print("  ✅ 用户体验: 文档美观、完整、无冗余")
    
    # 演示API使用
    print("\n🔧 API使用示例")
    print("-" * 30)
    
    print("原有方式 (仍然支持):")
    print("```python")
    print("# 扁平化数据")
    print("replacements = flatten_json_data(data)")
    print("# 处理PPT")
    print("process_all_slides_in_ppt(prs, replacements)")
    print("```")
    
    print("\n新的智能方式:")
    print("```python")
    print("# 一键智能处理")
    print("process_ppt_with_smart_features(prs, data)")
    print("# 或者")
    print("process_docx_with_smart_features(doc, data)")
    print("```")
    
    print("\n🎯 核心优势")
    print("-" * 30)
    print("1. 🧠 智能分析: 自动分析数据结构，识别内容模式")
    print("2. 🎨 美观输出: 自动清理空白，优化布局")
    print("3. 📄 完整内容: 自动分页，确保内容不丢失")
    print("4. 🔄 向后兼容: 完全兼容现有模板和API")
    print("5. ⚡ 零配置: 无需修改现有代码，即可享受智能功能")

if __name__ == "__main__":
    demonstrate_smart_processing()
