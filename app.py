from flask import Flask, request, jsonify, send_file, url_for, render_template, redirect, session, flash
from pptx import Presentation
from pptx.enum.text import PP_ALIGN
from docx import Document
import json
import os
import tempfile
from datetime import datetime, timedelta
import uuid
import pickle
import requests


app = Flask(__name__)
app.secret_key = 'your-secret-key-for-session'  # 用于session加密

# 确保generated_ppts目录存在
GENERATED_PPTS_DIR = 'generated_ppts'
os.makedirs(GENERATED_PPTS_DIR, exist_ok=True)

# 确保generated_docx目录存在
GENERATED_DOCX_DIR = 'generated_docx'
os.makedirs(GENERATED_DOCX_DIR, exist_ok=True)

# 确保8D报告数据目录存在
D8_REPORTS_DIR = '8d_reports'
os.makedirs(D8_REPORTS_DIR, exist_ok=True)

# 确保会话报告目录存在
SESSION_REPORTS_DIR = 'session_reports'
os.makedirs(SESSION_REPORTS_DIR, exist_ok=True)

# 文件映射存储文件
FILE_MAPPING_FILE = 'file_mapping.pkl'

# 文件映射字典：下载ID -> 文件信息
FILE_MAPPING = {}

# 下载链接有效期（天数）
DOWNLOAD_LINK_EXPIRE_DAYS = 7

# Dify API配置
DIFY_API_BASE_URL = 'http://10.60.120.129:81/v1'  # 您的Dify服务器API地址
DIFY_API_KEY = 'app-tRa2x4f3Nq0KZ1TEKDjn0NRs'  # 您的API Key
DIFY_APP_TYPE = 'chatbot'  # 应用类型：聊天助手

# 会话报告存储：session_id -> 报告数据
SESSION_REPORTS = {}


def load_file_mapping():
    """从文件加载文件映射"""
    global FILE_MAPPING
    try:
        if os.path.exists(FILE_MAPPING_FILE):
            with open(FILE_MAPPING_FILE, 'rb') as f:
                FILE_MAPPING = pickle.load(f)
            print(f"已加载 {len(FILE_MAPPING)} 个文件映射")
        else:
            FILE_MAPPING = {}
    except Exception as e:
        print(f"加载文件映射失败: {e}")
        FILE_MAPPING = {}


def save_file_mapping():
    """保存文件映射到文件"""
    try:
        with open(FILE_MAPPING_FILE, 'wb') as f:
            pickle.dump(FILE_MAPPING, f)
    except Exception as e:
        print(f"保存文件映射失败: {e}")


def cleanup_expired_mappings():
    """清理过期的文件映射"""
    current_time = datetime.now()
    expired_ids = []
    
    for download_id, file_info in list(FILE_MAPPING.items()):
        # 检查文件是否过期
        if 'created_at' in file_info:
            created_at = file_info['created_at']
            if current_time - created_at > timedelta(days=DOWNLOAD_LINK_EXPIRE_DAYS):
                expired_ids.append(download_id)
        # 检查文件是否还存在
        elif not os.path.exists(file_info['filepath']):
            expired_ids.append(download_id)
    
    for download_id in expired_ids:
        del FILE_MAPPING[download_id]
    
    if expired_ids:
        save_file_mapping()
        print(f"清理了 {len(expired_ids)} 个过期映射")


# 应用启动时加载文件映射
load_file_mapping()
cleanup_expired_mappings()


# 启动时清理过期会话
def cleanup_expired_sessions():
    """清理过期的会话数据"""
    try:
        current_time = datetime.now()
        expired_sessions = []
        
        # 清理内存中的过期会话
        for session_id, session_data in list(SESSION_REPORTS.items()):
            if 'created_at' in session_data:
                created_at = session_data['created_at']
                if current_time - created_at > timedelta(days=DOWNLOAD_LINK_EXPIRE_DAYS):
                    expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del SESSION_REPORTS[session_id]
        
        # 清理文件系统中的过期会话
        if os.path.exists(SESSION_REPORTS_DIR):
            for filename in os.listdir(SESSION_REPORTS_DIR):
                if filename.endswith('.json'):
                    filepath = os.path.join(SESSION_REPORTS_DIR, filename)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        if 'created_at' in data:
                            created_at = datetime.fromisoformat(data['created_at'])
                            if current_time - created_at > timedelta(days=DOWNLOAD_LINK_EXPIRE_DAYS):
                                os.remove(filepath)
                                print(f"已删除过期会话文件: {filename}")
                    except Exception as e:
                        print(f"清理会话文件 {filename} 时出错: {e}")
        
        if expired_sessions:
            print(f"清理了 {len(expired_sessions)} 个过期会话")
            
    except Exception as e:
        print(f"清理过期会话时出错: {e}")


# 清理过期会话
cleanup_expired_sessions()


def flatten_json_data(data, prefix=""):
    """扁平化JSON数据，提取所有键值对，包括数组中的数据"""
    flattened = {}

    if isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, dict):
                # 递归处理嵌套的字典
                flattened.update(flatten_json_data(value, prefix))
            elif isinstance(value, list):
                # 处理数组：遍历数组中的每个元素
                for item in value:
                    if isinstance(item, dict):
                        flattened.update(flatten_json_data(item, prefix))
            else:
                # 检查键是否是占位符格式
                if key.startswith("${") and key.endswith("}"):
                    flattened[key] = str(value)
                else:
                    # 如果不是占位符格式，也添加占位符格式的版本
                    placeholder_key = f"${{{key}}}"
                    flattened[placeholder_key] = str(value)
    elif isinstance(data, list):
        # 如果顶层就是数组，处理数组中的每个元素
        for item in data:
            if isinstance(item, dict):
                flattened.update(flatten_json_data(item, prefix))

    return flattened


def call_dify_api(message, conversation_id=None):
    """调用Dify聊天助手API"""
    try:
        headers = {
            'Authorization': f'Bearer {DIFY_API_KEY}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'query': message,
            'response_mode': 'blocking',
            'user': 'user-' + str(uuid.uuid4())[:8],
            'inputs': {}
        }
        
        if conversation_id:
            payload['conversation_id'] = conversation_id
        
        response = requests.post(
            f'{DIFY_API_BASE_URL}/chat-messages',
            headers=headers,
            json=payload,
            timeout=120  # 2分钟超时
        )
        
        print(f"API请求URL: {DIFY_API_BASE_URL}/chat-messages")
        print(f"API请求payload: {payload}")
        print(f"API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API响应内容: {result}")
            return result
        else:
            print(f"Dify API调用失败: {response.status_code}, {response.text}")
            return None
            
    except Exception as e:
        print(f"调用Dify API时出错: {str(e)}")
        return None


def generate_initial_report_with_ai(form_data, ai_requirements=""):
    """使用AI生成初始8D报告"""
    try:
        # 构建给AI的消息，包含表单数据和修改意见
        message_parts = []
        
        # # 添加基本指令
        # message_parts.append("请根据以下8D报告表单数据，生成完善的8D报告。请以JSON格式返回完整的D0-D8数据结构。")
        
        # 添加表单数据
        message_parts.append(f"表单数据：\n{json.dumps(form_data, ensure_ascii=False, indent=2)}")
        
        # 添加修改意见（如果有）
        if ai_requirements and ai_requirements.strip():
            message_parts.append(f"修改意见：{ai_requirements.strip()}")

        # # 添加格式要求
        # message_parts.append("请返回完善后的8D报告JSON数据，格式应该包含D0汇报信息、D1建立小组、D2问题描述、D3临时措施、D4根本原因、D5永久措施、D6措施验证、D7预防措施、D8庆贺团队等完整结构。")
        
        message = "\n\n".join(message_parts)
        
        # 调用Dify API生成初始报告
        api_response = call_dify_api(message)
        
        if api_response and 'answer' in api_response:
            ai_answer = api_response['answer']
            print(f"AI响应: {ai_answer}")
            
            # 尝试从AI响应中提取JSON数据
            try:
                # 查找JSON数据（假设AI会返回JSON格式的数据）
                import re
                json_match = re.search(r'\{.*\}', ai_answer, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    enhanced_data = json.loads(json_str)
                    print("成功解析AI返回的JSON数据")
                    return enhanced_data
                else:
                    print("AI响应中未找到有效的JSON数据")
                    return form_data
            except json.JSONDecodeError as e:
                print(f"解析AI返回的JSON数据失败: {e}")
                return form_data
        else:
            # 如果AI调用失败，返回原始表单数据
            print("AI生成失败，使用原始表单数据")
            return form_data
            
    except Exception as e:
        print(f"AI生成初始报告时出错: {str(e)}")
        return form_data


def refine_report_with_ai(current_report, user_feedback, conversation_id=None):
    """使用AI根据用户反馈完善报告"""
    try:
        # 构建给AI的消息
        message = f"""请根据用户反馈修改以下8D报告数据。

当前报告数据：
{json.dumps(current_report, ensure_ascii=False, indent=2)}

用户反馈：
{user_feedback}

请根据反馈修改报告，并以JSON格式返回完整的修改后的D0-D8数据结构。"""
        
        # 调用Dify API完善报告
        api_response = call_dify_api(message, conversation_id)
        
        if api_response and 'answer' in api_response:
            ai_answer = api_response['answer']
            conversation_id = api_response.get('conversation_id', conversation_id)
            
            # 尝试从AI响应中提取JSON数据
            try:
                import re
                json_match = re.search(r'\{.*\}', ai_answer, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    enhanced_data = json.loads(json_str)
                    return {
                        'report_data': enhanced_data,
                        'conversation_id': conversation_id,
                        'ai_response': ai_answer
                    }
                else:
                    # 如果没有找到JSON，返回AI的文本回复和原始数据
                    return {
                        'report_data': current_report,
                        'conversation_id': conversation_id,
                        'ai_response': ai_answer
                    }
            except json.JSONDecodeError as e:
                print(f"解析AI返回的JSON数据失败: {e}")
                return {
                    'report_data': current_report,
                    'conversation_id': conversation_id,
                    'ai_response': ai_answer
                }
        else:
            return {
                'report_data': current_report,
                'conversation_id': conversation_id,
                'ai_response': '抱歉，AI服务暂时不可用，请稍后再试。'
            }
            
    except Exception as e:
        print(f"AI完善报告时出错: {str(e)}")
        return {
            'report_data': current_report,
            'conversation_id': conversation_id,
            'ai_response': f'处理出错: {str(e)}'
        }


def save_session_report(session_id, report_data):
    """保存会话报告数据"""
    try:
        SESSION_REPORTS[session_id] = {
            'report_data': report_data,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        # 同时保存到文件系统
        filepath = os.path.join(SESSION_REPORTS_DIR, f"{session_id}.json")
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump({
                'report_data': report_data,
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
            
        return True
    except Exception as e:
        print(f"保存会话报告失败: {str(e)}")
        return False


def get_session_report(session_id):
    """获取会话报告数据"""
    try:
        # 先从内存中查找
        if session_id in SESSION_REPORTS:
            return SESSION_REPORTS[session_id]['report_data']
        
        # 从文件系统中加载
        filepath = os.path.join(SESSION_REPORTS_DIR, f"{session_id}.json")
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data['report_data']
        
        return None
    except Exception as e:
        print(f"获取会话报告失败: {str(e)}")
        return None


def copy_font_format(source_font, target_font):
    """复制字体格式"""
    try:
        if hasattr(source_font, 'name'):
            target_font.name = source_font.name
        if hasattr(source_font, 'size'):
            target_font.size = source_font.size
        if hasattr(source_font, 'bold'):
            target_font.bold = source_font.bold
        if hasattr(source_font, 'italic'):
            target_font.italic = source_font.italic
        if hasattr(source_font, 'underline'):
            target_font.underline = source_font.underline
        
        # 安全地复制颜色格式
        if hasattr(source_font, 'color') and source_font.color:
            try:
                # 检查颜色是否有rgb属性
                if hasattr(source_font.color, 'rgb') and source_font.color.rgb:
                    target_font.color.rgb = source_font.color.rgb
                # 对于预设颜色(_PrstColor)，尝试复制颜色类型
                elif hasattr(source_font.color, 'type'):
                    # 只有当目标字体的颜色也支持相同的颜色类型时才复制
                    if hasattr(target_font.color, 'type'):
                        target_font.color.type = source_font.color.type
                    # 对于scheme colors，尝试复制theme_color
                    if hasattr(source_font.color, 'theme_color') and hasattr(target_font.color, 'theme_color'):
                        target_font.color.theme_color = source_font.color.theme_color
            except AttributeError:
                # 如果颜色类型不兼容，跳过颜色复制
                pass
    except Exception as e:
        print(f"复制字体格式时出现警告: {str(e)}")


def copy_paragraph_format(source_paragraph, target_paragraph):
    """复制段落格式"""
    try:
        if hasattr(source_paragraph, 'alignment'):
            target_paragraph.alignment = source_paragraph.alignment
        if hasattr(source_paragraph, 'space_before'):
            target_paragraph.space_before = source_paragraph.space_before
        if hasattr(source_paragraph, 'space_after'):
            target_paragraph.space_after = source_paragraph.space_after
        if hasattr(source_paragraph, 'line_spacing'):
            target_paragraph.line_spacing = source_paragraph.line_spacing
    except Exception as e:
        print(f"复制段落格式时出现警告: {str(e)}")


def find_placeholders_in_shape(shape):
    """查找形状中的所有占位符"""
    placeholders = set()
    
    def extract_from_text(text):
        """从文本中提取占位符"""
        start = 0
        while True:
            start = text.find("${", start)
            if start == -1:
                break
            end = text.find("}", start)
            if end == -1:
                break
            placeholders.add(text[start:end + 1])
            start = end + 1
    
    if hasattr(shape, "table"):
        # 处理表格中的占位符
        for row in shape.table.rows:
            for cell in row.cells:
                if hasattr(cell, "text"):
                    extract_from_text(cell.text)
    elif hasattr(shape, "text"):
        # 处理普通文本中的占位符
        extract_from_text(shape.text)
    
    return placeholders


def find_all_placeholders_in_slide(slide):
    """查找幻灯片中的所有占位符"""
    all_placeholders = set()
    for shape in slide.shapes:
        all_placeholders.update(find_placeholders_in_shape(shape))
    return all_placeholders


def process_table_cell(cell, replacements):
    """处理表格单元格中的文本替换"""
    if not hasattr(cell, "text_frame"):
        return False

    original_text = cell.text
    modified_text = original_text
    has_replacement = False

    # 保存原始格式
    original_runs = []
    try:
        for paragraph in cell.text_frame.paragraphs:
            for run in paragraph.runs:
                original_runs.append({
                    'text': run.text,
                    'font': run.font,
                    'paragraph': paragraph
                })
    except Exception:
        pass

    # 执行所有可能的替换
    for key, value in replacements.items():
        if key in modified_text:
            modified_text = modified_text.replace(key, value)
            has_replacement = True

    # 删除未替换的占位符
    while True:
        start = modified_text.find("${")
        if start == -1:
            break
        end = modified_text.find("}", start)
        if end == -1:
            break
        # 删除占位符
        placeholder = modified_text[start:end + 1]
        modified_text = modified_text.replace(placeholder, "")
        has_replacement = True

    # 如果文本有变化，应用新文本并保持格式
    if modified_text != original_text:
        try:
            text_frame = cell.text_frame
            text_frame.clear()
            p = text_frame.paragraphs[0]
            
            if original_runs:
                run = p.add_run()
                run.text = modified_text
                copy_font_format(original_runs[0]['font'], run.font)
                copy_paragraph_format(original_runs[0]['paragraph'], p)
            else:
                p.text = modified_text
        except Exception:
            pass
    
    return has_replacement


def process_table(table, replacements):
    """处理表格中的所有单元格"""
    has_replacement = False
    for row in table.rows:
        for cell in row.cells:
            if process_table_cell(cell, replacements):
                has_replacement = True
    return has_replacement


def process_shape_text(shape, replacements):
    """处理形状中的文本替换"""
    has_replacement = False
    
    # 1. 处理表格
    if hasattr(shape, "table"):
        return process_table(shape.table, replacements)
    
    # 2. 处理组合形状(Group) - 递归处理子形状
    if hasattr(shape, "shapes"):
        for sub_shape in shape.shapes:
            if process_shape_text(sub_shape, replacements):
                has_replacement = True
        return has_replacement
    
    # 3. 处理图表中的文本
    if hasattr(shape, "chart"):
        if process_chart_text(shape.chart, replacements):
            has_replacement = True
    
    # 4. 处理文本框和其他有text_frame的形状
    if hasattr(shape, "text_frame") and shape.text_frame:
        if process_text_frame(shape.text_frame, replacements):
            has_replacement = True
    
    # 5. 处理普通文本形状（向后兼容）
    elif hasattr(shape, "text") and shape.text:
        original_text = shape.text
        modified_text = original_text
        
        # 执行所有可能的替换
        for key, value in replacements.items():
            if key in modified_text:
                modified_text = modified_text.replace(key, value)
                has_replacement = True
        
        # 删除未替换的占位符
        while True:
            start = modified_text.find("${")
            if start == -1:
                break
            end = modified_text.find("}", start)
            if end == -1:
                break
            placeholder = modified_text[start:end + 1]
            modified_text = modified_text.replace(placeholder, "")
            has_replacement = True
        
        # 如果文本有变化，应用新文本并保持格式
        if modified_text != original_text:
            try:
                if hasattr(shape, 'text_frame') and shape.text_frame:
                    process_text_frame_with_text(shape.text_frame, modified_text)
                else:
                    shape.text = modified_text
            except Exception as e:
                pass  # 静默处理格式错误
    
    return has_replacement


def process_text_frame(text_frame, replacements):
    """处理text_frame中的文本替换"""
    has_replacement = False
    
    try:
        for paragraph in text_frame.paragraphs:
            # 获取段落的完整文本
            original_text = paragraph.text
            if not original_text:
                continue
                
            modified_text = original_text
            
            # 执行所有可能的替换
            for key, value in replacements.items():
                if key in modified_text:
                    modified_text = modified_text.replace(key, value)
                    has_replacement = True
            
            # 删除未替换的占位符
            while True:
                start = modified_text.find("${")
                if start == -1:
                    break
                end = modified_text.find("}", start)
                if end == -1:
                    break
                placeholder = modified_text[start:end + 1]
                modified_text = modified_text.replace(placeholder, "")
                has_replacement = True
            
            # 如果文本有变化，更新段落
            if modified_text != original_text:
                # 保存原始格式信息
                original_runs = []
                for run in paragraph.runs:
                    original_runs.append({
                        'text': run.text,
                        'font': run.font
                    })
                
                # 清除段落内容
                paragraph.clear()
                
                # 添加新内容并保持格式
                if original_runs and modified_text:
                    run = paragraph.add_run()
                    run.text = modified_text
                    try:
                        copy_font_format(original_runs[0]['font'], run.font)
                    except Exception:
                        pass
                elif modified_text:
                    paragraph.add_run().text = modified_text
                
    except Exception as e:
        pass  # 静默处理错误
    
    return has_replacement


def process_text_frame_with_text(text_frame, new_text):
    """使用新文本更新text_frame，保持格式"""
    try:
        # 保存第一个段落的格式
        original_runs = []
        if text_frame.paragraphs:
            for run in text_frame.paragraphs[0].runs:
                original_runs.append({
                    'font': run.font
                })
        
        # 清除内容
        text_frame.clear()
        
        # 添加新内容
        if new_text:
            p = text_frame.paragraphs[0]
            run = p.add_run()
            run.text = new_text
            
            # 尝试恢复格式
            if original_runs:
                try:
                    copy_font_format(original_runs[0]['font'], run.font)
                except Exception:
                    pass
                    
    except Exception as e:
        print(f"    更新text_frame文本时出错: {e}")


def process_chart_text(chart, replacements):
    """处理图表中的文本替换"""
    has_replacement = False
    
    try:
        # 处理图表标题
        if hasattr(chart, 'chart_title') and chart.chart_title and hasattr(chart.chart_title, 'text_frame'):
            if process_text_frame(chart.chart_title.text_frame, replacements):
                has_replacement = True
        
        # 处理坐标轴标签
        if hasattr(chart, 'axes'):
            for axis in chart.axes:
                if hasattr(axis, 'axis_title') and axis.axis_title and hasattr(axis.axis_title, 'text_frame'):
                    if process_text_frame(axis.axis_title.text_frame, replacements):
                        has_replacement = True
        
        # 处理数据标签（这个比较复杂，暂时跳过具体实现）
        # TODO: 如果需要处理数据标签，可以在这里添加
        
    except Exception as e:
        pass  # 静默处理错误
    
    return has_replacement


def process_slide(slide, replacements):
    """处理单个幻灯片"""
    for shape in slide.shapes:
        process_shape_text(shape, replacements)


def process_all_slides_in_ppt(prs, replacements):
    """处理PPT中的所有幻灯片"""
    print("🔄 开始处理PPT模板...")
    
    # 收集所有未匹配的占位符
    all_unmatched = set()
    
    for slide_idx, slide in enumerate(prs.slides):
        # 查找当前页面的所有占位符
        placeholders = find_all_placeholders_in_slide(slide)
        
        # 检查哪些占位符没有匹配的数据
        for placeholder in placeholders:
            if placeholder not in replacements or not replacements[placeholder]:
                all_unmatched.add(placeholder)
        
        # 处理当前页面
        process_slide(slide, replacements)
    
    # 只输出未匹配的占位符
    if all_unmatched:
        print(f"⚠️  PPT中未匹配到数据的占位符 ({len(all_unmatched)}个):")
        for placeholder in sorted(all_unmatched):
            print(f"   - {placeholder}")
    else:
        print("✅ PPT中所有占位符均已匹配")
    
    print("✅ PPT处理完成")


# DOCX 处理相关函数
def find_all_tables_in_docx(doc):
    """查找docx文档中的所有表格（包括嵌套表格）"""
    all_tables = []
    
    # 添加主表格
    all_tables.extend(doc.tables)
    
    # 查找嵌套表格
    def find_nested_tables(tables):
        nested = []
        for table in tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.tables:
                        nested.extend(cell.tables)
                        # 递归查找更深层的嵌套
                        nested.extend(find_nested_tables(cell.tables))
        return nested
    
    # 递归查找所有嵌套表格
    nested_tables = find_nested_tables(doc.tables)
    all_tables.extend(nested_tables)
    
    return all_tables


def process_docx_table_cell(cell, replacements):
    """处理docx表格单元格中的文本替换"""
    has_replacement = False
    
    for para_idx, paragraph in enumerate(cell.paragraphs):
        original_text = paragraph.text
        
        modified_text = original_text
        
        # 执行所有可能的替换
        for key, value in replacements.items():
            if key in modified_text:
                modified_text = modified_text.replace(key, value)
                has_replacement = True
        
        # 删除未替换的占位符
        while True:
            start = modified_text.find("${")
            if start == -1:
                break
            end = modified_text.find("}", start)
            if end == -1:
                break
            # 删除占位符
            placeholder = modified_text[start:end + 1]
            modified_text = modified_text.replace(placeholder, "")
            has_replacement = True
        
        # 如果文本有变化，更新段落文本
        if modified_text != original_text:
            # 保存原始格式
            runs_data = []
            for run in paragraph.runs:
                runs_data.append({
                    'text': run.text,
                    'bold': run.bold,
                    'italic': run.italic,
                    'underline': run.underline,
                    'font_name': run.font.name,
                    'font_size': run.font.size
                })
            
            # 清除段落内容
            paragraph.clear()
            
            # 添加新内容并尝试保持格式
            if runs_data:
                new_run = paragraph.add_run(modified_text)
                try:
                    new_run.bold = runs_data[0]['bold']
                    new_run.italic = runs_data[0]['italic']
                    new_run.underline = runs_data[0]['underline']
                    if runs_data[0]['font_name']:
                        new_run.font.name = runs_data[0]['font_name']
                    if runs_data[0]['font_size']:
                        new_run.font.size = runs_data[0]['font_size']
                except Exception:
                    pass
            else:
                paragraph.add_run(modified_text)
    
    return has_replacement


def process_docx_table(table, replacements, table_idx):
    """处理docx表格中的所有单元格"""
    has_replacement = False
    for row_idx, row in enumerate(table.rows):
        for cell_idx, cell in enumerate(row.cells):
            cell_text = cell.text.strip()
            if cell_text and ("${" in cell_text):  # 只处理包含占位符的单元格
                if process_docx_table_cell(cell, replacements):
                    has_replacement = True
    
    return has_replacement


def process_docx_paragraphs(doc, replacements):
    """处理docx文档中的所有段落"""
    has_replacement = False
    
    for para_idx, paragraph in enumerate(doc.paragraphs):
        original_text = paragraph.text
        if original_text.strip() and ("${" in original_text):  # 只处理包含占位符的段落
            modified_text = original_text
            
            # 执行所有可能的替换
            for key, value in replacements.items():
                if key in modified_text:
                    modified_text = modified_text.replace(key, value)
                    has_replacement = True
            
            # 删除未替换的占位符
            while True:
                start = modified_text.find("${")
                if start == -1:
                    break
                end = modified_text.find("}", start)
                if end == -1:
                    break
                placeholder = modified_text[start:end + 1]
                modified_text = modified_text.replace(placeholder, "")
                has_replacement = True
            
            # 如果文本有变化，更新段落文本
            if modified_text != original_text:
                # 保存原始格式
                runs_data = []
                for run in paragraph.runs:
                    runs_data.append({
                        'text': run.text,
                        'bold': run.bold,
                        'italic': run.italic,
                        'underline': run.underline,
                        'font_name': run.font.name,
                        'font_size': run.font.size
                    })
                
                # 清除段落内容
                paragraph.clear()
                
                # 添加新内容并尝试保持格式
                if runs_data:
                    new_run = paragraph.add_run(modified_text)
                    try:
                        new_run.bold = runs_data[0]['bold']
                        new_run.italic = runs_data[0]['italic']
                        new_run.underline = runs_data[0]['underline']
                        if runs_data[0]['font_name']:
                            new_run.font.name = runs_data[0]['font_name']
                        if runs_data[0]['font_size']:
                            new_run.font.size = runs_data[0]['font_size']
                    except Exception:
                        pass
                else:
                    paragraph.add_run(modified_text)
    
    return has_replacement


def process_all_content_in_docx(doc, replacements):
    """处理DOCX中的所有内容"""
    print("🔄 开始处理DOCX模板...")
    
    # 收集所有未匹配的占位符
    all_unmatched = set()
    
    # 查找文档中的所有占位符
    for paragraph in doc.paragraphs:
        text = paragraph.text
        if "${" in text:
            # 提取占位符
            import re
            placeholders = re.findall(r'\$\{[^}]+\}', text)
            for placeholder in placeholders:
                if placeholder not in replacements or not replacements[placeholder]:
                    all_unmatched.add(placeholder)
    
    # 查找表格中的占位符
    all_tables = find_all_tables_in_docx(doc)
    for table in all_tables:
        for row in table.rows:
            for cell in row.cells:
                text = cell.text
                if "${" in text:
                    import re
                    placeholders = re.findall(r'\$\{[^}]+\}', text)
                    for placeholder in placeholders:
                        if placeholder not in replacements or not replacements[placeholder]:
                            all_unmatched.add(placeholder)
    
    # 处理所有段落
    process_docx_paragraphs(doc, replacements)
    
    # 处理所有表格
    for table_idx, table in enumerate(all_tables):
        process_docx_table(table, replacements, table_idx)
    
    # 只输出未匹配的占位符
    if all_unmatched:
        print(f"⚠️  DOCX中未匹配到数据的占位符 ({len(all_unmatched)}个):")
        for placeholder in sorted(all_unmatched):
            print(f"   - {placeholder}")
    else:
        print("✅ DOCX中所有占位符均已匹配")
    
    print("✅ DOCX处理完成")


# 路由处理函数
@app.route('/process_ppt', methods=['POST'])
def process_ppt():
    try:
        data = request.get_json()
        
        if not data or 'template_name' not in data or 'replacements' not in data:
            return jsonify({'error': '无效的请求数据'}), 400
            
        template_name = data['template_name']
        replacements_data = data['replacements']
        
        if not os.path.exists(template_name):
            return jsonify({'error': f'模板文件 {template_name} 不存在'}), 404
            
        # 加载PPT模板
        prs = Presentation(template_name)
        
        # 扁平化所有替换数据
        all_replacements = flatten_json_data(replacements_data)
        print(f"扁平化后的替换数据: {all_replacements}")
        
        # 处理整个PPT
        process_all_slides_in_ppt(prs, all_replacements)
        
        # 生成唯一的文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        random_suffix = str(uuid.uuid4())[:8]
        output_filename = f"{timestamp}_{random_suffix}.pptx"
        output_path = os.path.join(GENERATED_PPTS_DIR, output_filename)
        
        # 保存处理后的PPT
        prs.save(output_path)
        
        # 生成下载ID
        download_id = str(uuid.uuid4())
        FILE_MAPPING[download_id] = {
            'filepath': output_path,
            'filename': output_filename,
            'type': 'pptx',
            'created_at': datetime.now()
        }
        save_file_mapping()
        
        # 构建下载链接（使用UUID）
        download_url = f"http://{request.host}/download_file/{download_id}"
        
        return jsonify({
            'status': 'success',
            'message': 'PPT生成成功',
            'download_url': download_url
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/process_docx', methods=['POST'])
def process_docx():
    try:
        data = request.get_json()
        
        if not data or 'template_name' not in data or 'replacements' not in data:
            return jsonify({'error': '无效的请求数据'}), 400
            
        template_name = data['template_name']
        replacements_data = data['replacements']
        
        if not os.path.exists(template_name):
            return jsonify({'error': f'模板文件 {template_name} 不存在'}), 404
            
        # 加载DOCX模板
        doc = Document(template_name)
        print(f"\n加载DOCX文档: {template_name}")
        
        # 扁平化所有替换数据
        all_replacements = flatten_json_data(replacements_data)
        print(f"扁平化后的替换数据: {all_replacements}")
        
        # 处理整个DOCX文档
        process_all_content_in_docx(doc, all_replacements)
        
        # 生成唯一的文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        random_suffix = str(uuid.uuid4())[:8]
        output_filename = f"{timestamp}_{random_suffix}.docx"
        output_path = os.path.join(GENERATED_DOCX_DIR, output_filename)
        
        # 保存处理后的DOCX
        doc.save(output_path)
        print(f"文件已保存: {output_path}")
        
        # 生成下载ID
        download_id = str(uuid.uuid4())
        FILE_MAPPING[download_id] = {
            'filepath': output_path,
            'filename': output_filename,
            'type': 'docx',
            'created_at': datetime.now()
        }
        save_file_mapping()
        
        # 构建下载链接（使用UUID）
        download_url = f"http://{request.host}/download_file/{download_id}"
        
        return jsonify({
            'status': 'success',
            'message': 'DOCX生成成功',
            'download_url': download_url
        })
        
    except Exception as e:
        print(f"处理DOCX时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500


@app.route('/download/<download_id>')
def download_page(download_id):
    """显示下载页面"""
    try:
        if download_id not in FILE_MAPPING:
            return render_template('download.html', 
                                   error="下载链接无效或已过期，请重新生成文档。")
        
        file_info = FILE_MAPPING[download_id]
        filepath = file_info['filepath']
        filename = file_info['filename']
        file_type = file_info['type'].upper()
        
        if not os.path.exists(filepath):
            return render_template('download.html', 
                                   error="文件不存在，可能已被删除。")
        
        return render_template('download.html',
                               download_id=download_id,
                               filename=filename,
                               file_type=file_type,
                               error=None)
    except Exception as e:
        return render_template('download.html', error=f"页面加载失败: {str(e)}")


@app.route('/download_file/<download_id>')
def download_file(download_id):
    """实际下载文件"""
    try:
        if download_id not in FILE_MAPPING:
            return jsonify({'error': '下载链接无效或已过期'}), 404
        
        file_info = FILE_MAPPING[download_id]
        filepath = file_info['filepath']
        filename = file_info['filename']
        file_type = file_info['type']
        
        if not os.path.exists(filepath):
            return jsonify({'error': '文件不存在'}), 404
        
        # 根据文件类型设置MIME类型
        if file_type == 'pptx':
            mimetype = 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        elif file_type == 'docx':
            mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        else:
            mimetype = 'application/octet-stream'
        
        return send_file(
            filepath,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )
    except Exception as e:
        return jsonify({'error': f'文件下载失败: {str(e)}'}), 404


@app.route('/admin/cleanup', methods=['POST'])
def cleanup_files():
    """清理无效的文件映射（可选的管理功能）"""
    try:
        cleaned_count = 0
        invalid_ids = []
        
        for download_id, file_info in list(FILE_MAPPING.items()):
            if not os.path.exists(file_info['filepath']):
                invalid_ids.append(download_id)
                cleaned_count += 1
        
        for download_id in invalid_ids:
            del FILE_MAPPING[download_id]
        
        save_file_mapping()
        
        return jsonify({
            'status': 'success',
            'message': f'清理完成，删除了 {cleaned_count} 个无效映射'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/')
def index():
    """主页 - 显示8D报告表单"""
    try:
        # 获取flash消息
        return render_template('d8_form.html')
    except Exception as e:
        return f"页面加载失败: {str(e)}", 500


@app.route('/submit_8d_report', methods=['POST'])
def submit_8d_report():
    """处理8D报告表单提交，返回JSON响应并启动AI协作流程"""
    try:
        # 只处理新的JSON格式请求
        raw_data = request.get_data(as_text=True)
        
        # 检查是否包含修改意见
        ai_requirements = ""
        if raw_data.startswith('修改意见：'):
            # 解析修改意见和表单数据
            parts = raw_data.split('\n\n表单数据：', 1)
            if len(parts) == 2:
                ai_requirements = parts[0].replace('修改意见：', '').strip()
                form_data = json.loads(parts[1])
            else:
                raise ValueError("请求格式错误：无法解析修改意见和表单数据")
        else:
            # 直接解析JSON（没有修改意见）
            form_data = json.loads(raw_data)
        
        # 打印提交的D0-D8数据
        print("\n" + "=" * 80)
        print("📋 接收到8D报告提交数据:")
        print("=" * 80)
        print(json.dumps(form_data, ensure_ascii=False, indent=2))
        if ai_requirements:
            print(f"🤖 AI修改意见: {ai_requirements}")
        print("=" * 80 + "\n")
        
        # 生成会话ID
        session_id = str(uuid.uuid4())
        
        # 使用AI生成初始报告（如果配置了Dify API）
        enhanced_data = form_data  # 默认使用原始数据
        if DIFY_API_KEY and DIFY_API_KEY.startswith('app-'):
            print("正在使用AI生成初始报告...")
            try:
                enhanced_data = generate_initial_report_with_ai(form_data, ai_requirements)
                if enhanced_data != form_data:
                    print("\n" + "=" * 80)
                    print("🤖 AI优化后的8D报告数据:")
                    print("=" * 80)
                    print(json.dumps(enhanced_data, ensure_ascii=False, indent=2))
                    print("=" * 80 + "\n")
            except Exception as ai_error:
                print(f"AI生成失败，使用原始数据: {ai_error}")
        else:
            print("未配置AI服务，使用原始表单数据")
        
        # 保存会话报告
        save_session_report(session_id, enhanced_data)
        
        # 返回JSON响应
        return jsonify({
            'status': 'success',
            'message': 'AI正在生成初始报告...',
            'session_id': session_id,
            'enhanced_data': enhanced_data if enhanced_data != form_data else None
        })
        
    except Exception as e:
        error_msg = f"处理失败: {str(e)}"
        print(error_msg)
        
        return jsonify({
            'status': 'error',
            'error': error_msg
        }), 500


@app.route('/generate_direct_report', methods=['POST'])
def generate_direct_report():
    """直接生成8D报告，不使用AI优化"""
    try:
        # 只处理新的JSON格式请求
        raw_data = request.get_data(as_text=True)
        
        # 对于直接生成报告，可能不包含修改意见，直接解析JSON
        try:
            form_data = json.loads(raw_data)
        except json.JSONDecodeError:
            # 如果解析失败，可能是包含修改意见的格式，但对于直接生成我们忽略修改意见
            if raw_data.startswith('修改意见：'):
                parts = raw_data.split('\n\n表单数据：', 1)
                if len(parts) == 2:
                    form_data = json.loads(parts[1])
                else:
                    raise ValueError("无法解析请求数据")
            else:
                raise ValueError("无法解析请求数据")
        
        # 打印提交的D0-D8数据
        print("\n" + "=" * 80)
        print("📋 接收到直接生成报告请求:")
        print("=" * 80)
        print(json.dumps(form_data, ensure_ascii=False, indent=2))
        print("=" * 80 + "\n")
        
        # 生成会话ID
        session_id = str(uuid.uuid4())
        
        # 直接使用原始表单数据，不进行AI优化
        print("直接生成报告，不使用AI优化")
        
        # 保存会话报告
        save_session_report(session_id, form_data)
        
        # 直接使用表单数据作为替换数据
        replacements_data = form_data
        
        # 生成PPT和DOCX文件
        try:
            # 使用现有的/process_ppt和/process_docx路由的逻辑
            # 选择模板版本（0530 或 0610）
            template_version = '0530'  # 默认使用0530模板
            template_name = f"8d_template/{template_version}/template.pptx"
            docx_template_name = f"8d_template/{template_version}/template.docx"
            
            # 如果模板不存在，回退到根目录
            if not os.path.exists(template_name):
                template_name = "template.pptx"
            if not os.path.exists(docx_template_name):
                docx_template_name = "template.docx"
            
            # 生成PPT
            ppt_result = None
            if os.path.exists(template_name):
                # 调用现有的PPT处理逻辑
                prs = Presentation(template_name)
                all_replacements = flatten_json_data(replacements_data)
                process_all_slides_in_ppt(prs, all_replacements)
                
                # 生成唯一的文件名
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                random_suffix = str(uuid.uuid4())[:8]
                output_filename = f"{timestamp}_{random_suffix}.pptx"
                output_path = os.path.join(GENERATED_PPTS_DIR, output_filename)
                
                # 保存处理后的PPT
                prs.save(output_path)
                
                # 生成下载ID
                download_id = str(uuid.uuid4())
                FILE_MAPPING[download_id] = {
                    'filepath': output_path,
                    'filename': output_filename,
                    'type': 'pptx',
                    'created_at': datetime.now()
                }
                save_file_mapping()
                
                ppt_result = {
                    'status': 'success',
                    'download_url': f"http://{request.host}/download_file/{download_id}",
                    'filename': output_filename
                }
            
            # 生成DOCX
            docx_result = None
            if os.path.exists(docx_template_name):
                # 调用现有的DOCX处理逻辑
                doc = Document(docx_template_name)
                all_replacements = flatten_json_data(replacements_data)
                process_all_content_in_docx(doc, all_replacements)
                
                # 生成唯一的文件名
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                random_suffix = str(uuid.uuid4())[:8]
                output_filename = f"{timestamp}_{random_suffix}.docx"
                output_path = os.path.join(GENERATED_DOCX_DIR, output_filename)
                
                # 保存处理后的DOCX
                doc.save(output_path)
                
                # 生成下载ID
                download_id = str(uuid.uuid4())
                FILE_MAPPING[download_id] = {
                    'filepath': output_path,
                    'filename': output_filename,
                    'type': 'docx',
                    'created_at': datetime.now()
                }
                save_file_mapping()
                
                docx_result = {
                    'status': 'success',
                    'download_url': f"http://{request.host}/download_file/{download_id}",
                    'filename': output_filename
                }
            
            success_message = "报告生成完成！"
            files = {}
            if ppt_result and ppt_result.get('status') == 'success':
                success_message += " PPT文件已生成"
                files['ppt'] = ppt_result['download_url']
            if docx_result and docx_result.get('status') == 'success':
                success_message += " DOCX文件已生成"
                files['docx'] = docx_result['download_url']
            
            if not files:
                raise Exception("模板文件不存在，无法生成文档")
            
        except Exception as gen_error:
            print(f"文件生成出错: {gen_error}")
            return jsonify({
                'status': 'error',
                'error': f"文件生成失败: {str(gen_error)}"
            }), 500
        
        # 返回JSON响应
        return jsonify({
            'status': 'success',
            'message': success_message,
            'session_id': session_id,
            'files': files
        })
        
    except Exception as e:
        error_msg = f"直接生成报告失败: {str(e)}"
        print(error_msg)
        
        return jsonify({
            'status': 'error',
            'error': error_msg
        }), 500


@app.route('/submit_8d_report_original', methods=['POST'])
def submit_8d_report_original():
    """原始的8D报告表单提交处理（保留作为备用）"""
    try:
        # 收集表单数据
        form_data = request.form.to_dict()
        
        # 定义所有可能的表单字段映射到JSON占位符
        # 这确保即使字段为空也会在JSON中显示
        base_field_mapping = {
            # D0 基本信息
            'd0_title': '${D0标题}',
            'd0_reporter': '${D0汇报人}',
            'd0_time': '${D0汇报时间}',
            'd0_background': '${D0项目背景}',
            
            # D1 组长信息
            'd1_leader_name': '${D1组长姓名}',
            'd1_leader_dept': '${D1组长部门}',
            'd1_leader_position': '${D1组长职位}',
            'd1_leader_responsibility': '${D1组长主要职责}',
            
            # D2 问题描述
            'd2_description': '${D2事件整体描述}',
            'd2_when': '${D2何时发生}',
            'd2_where': '${D2何地发生}',
            'd2_who': '${D2何人发现}',
            'd2_what': '${D2发生了什么问题}',
            'd2_why': '${D2为什么是这问题}',
            'd2_how': '${D2问题如何发生}',
            'd2_impact': '${D2问题影响程度}',
            
            # D4 5Why分析
            'd4_why1': '${D4why1}',
            'd4_answer1': '${D4answer1}',
            'd4_why2': '${D4why2}',
            'd4_answer2': '${D4answer2}',
            'd4_why3': '${D4why3}',
            'd4_answer3': '${D4answer3}',
            'd4_why4': '${D4why4}',
            'd4_answer4': '${D4answer4}',
            'd4_why5': '${D4why5}',
            'd4_answer5': '${D4answer5}',
            'd4_summary': '${D4原因小结}',
            
            # D8 庆贺团队
            'd8_effectiveness': '${D8有效性确认}',
            'd8_confirmer': '${D8确认人}',
            'd8_confirm_time': '${D8确认完成时间}'
        }
        
        # 构建基础JSON数据结构，确保所有基础字段都存在
        json_data = {
            "template_name": "template.pptx",
            "replacements": {
                "D0汇报信息": {
                    base_field_mapping['d0_title']: form_data.get('d0_title', ''),
                    base_field_mapping['d0_reporter']: form_data.get('d0_reporter', ''),
                    base_field_mapping['d0_time']: form_data.get('d0_time', ''),
                    base_field_mapping['d0_background']: form_data.get('d0_background', '')
                },
                "D1建立小组": {
                    "组长": {
                        base_field_mapping['d1_leader_name']: form_data.get('d1_leader_name', ''),
                        base_field_mapping['d1_leader_dept']: form_data.get('d1_leader_dept', ''),
                        base_field_mapping['d1_leader_position']: form_data.get('d1_leader_position', ''),
                        base_field_mapping['d1_leader_responsibility']: form_data.get('d1_leader_responsibility', '')
                    }
                },
                "D2问题描述": {
                    base_field_mapping['d2_description']: form_data.get('d2_description', ''),
                    "5W2H": {
                        base_field_mapping['d2_when']: form_data.get('d2_when', ''),
                        base_field_mapping['d2_where']: form_data.get('d2_where', ''),
                        base_field_mapping['d2_who']: form_data.get('d2_who', ''),
                        base_field_mapping['d2_why']: form_data.get('d2_why', ''),
                        base_field_mapping['d2_what']: form_data.get('d2_what', ''),
                        base_field_mapping['d2_how']: form_data.get('d2_how', ''),
                        base_field_mapping['d2_impact']: form_data.get('d2_impact', '')
                    }
                },
                "D3临时措施": {},
                "D4根本原因": {
                    "5why分析": {
                        base_field_mapping['d4_why1']: form_data.get('d4_why1', ''),
                        base_field_mapping['d4_answer1']: form_data.get('d4_answer1', ''),
                        base_field_mapping['d4_why2']: form_data.get('d4_why2', ''),
                        base_field_mapping['d4_answer2']: form_data.get('d4_answer2', ''),
                        base_field_mapping['d4_why3']: form_data.get('d4_why3', ''),
                        base_field_mapping['d4_answer3']: form_data.get('d4_answer3', ''),
                        base_field_mapping['d4_why4']: form_data.get('d4_why4', ''),
                        base_field_mapping['d4_answer4']: form_data.get('d4_answer4', ''),
                        base_field_mapping['d4_why5']: form_data.get('d4_why5', ''),
                        base_field_mapping['d4_answer5']: form_data.get('d4_answer5', '')
                    },
                    "人机料法环测分析": {},
                    base_field_mapping['d4_summary']: form_data.get('d4_summary', '')
                },
                "D5永久措施": {},
                "D6措施验证": {},
                "D7预防措施": {},
                "D8庆贺团队": {
                    base_field_mapping['d8_effectiveness']: form_data.get('d8_effectiveness', ''),
                    base_field_mapping['d8_confirmer']: form_data.get('d8_confirmer', ''),
                    base_field_mapping['d8_confirm_time']: form_data.get('d8_confirm_time', '')
                }
            }
        }
        
        # 添加D1成员信息 - 确保至少包含成员1和成员2的空结构
        for member_num in [1, 2]:
            json_data["replacements"]["D1建立小组"][f"成员{member_num}"] = {
                f"${{D1成员{member_num}姓名}}": form_data.get(f'd1_member{member_num}_name', ''),
                f"${{D1成员{member_num}部门}}": form_data.get(f'd1_member{member_num}_dept', ''),
                f"${{D1成员{member_num}职位}}": form_data.get(f'd1_member{member_num}_position', ''),
                f"${{D1成员{member_num}主要职责}}": form_data.get(f'd1_member{member_num}_responsibility', '')
            }
        
        # 动态添加额外的成员信息
        member_numbers = set()
        for key in form_data.keys():
            if key.startswith('d1_member') and key.endswith('_name'):
                try:
                    member_num = int(key.split('_')[1].replace('member', ''))
                    if member_num > 2:  # 只处理成员3及以上
                        member_numbers.add(member_num)
                except (IndexError, ValueError):
                    continue
        
        # 为额外成员添加数据
        for i in sorted(member_numbers):
            json_data["replacements"]["D1建立小组"][f"成员{i}"] = {
                f"${{D1成员{i}姓名}}": form_data.get(f'd1_member{i}_name', ''),
                f"${{D1成员{i}部门}}": form_data.get(f'd1_member{i}_dept', ''),
                f"${{D1成员{i}职位}}": form_data.get(f'd1_member{i}_position', ''),
                f"${{D1成员{i}主要职责}}": form_data.get(f'd1_member{i}_responsibility', '')
            }
        
        # 添加D3临时措施信息 - 确保至少包含措施1的空结构
        json_data["replacements"]["D3临时措施"]["临时措施1"] = {
            "${D3范围1}": form_data.get('d3_scope1', ''),
            "${D3处置对策1}": form_data.get('d3_measure1', ''),
            "${D3责任人1}": form_data.get('d3_responsible1', ''),
            "${D3完成期限1}": form_data.get('d3_deadline1', ''),
            "${D3状态1}": form_data.get('d3_status1', ''),
            "${D3进度备注1}": form_data.get('d3_note1', '')
        }
        
        # 动态添加额外的D3措施
        measure_numbers = set()
        for key in form_data.keys():
            if key.startswith('d3_scope') and key[8:].isdigit():
                try:
                    measure_num = int(key[8:])
                    if measure_num > 1:  # 只处理措施2及以上
                        measure_numbers.add(measure_num)
                except ValueError:
                    continue
        
        for i in sorted(measure_numbers):
            json_data["replacements"]["D3临时措施"][f"临时措施{i}"] = {
                f"${{D3范围{i}}}": form_data.get(f'd3_scope{i}', ''),
                f"${{D3处置对策{i}}}": form_data.get(f'd3_measure{i}', ''),
                f"${{D3责任人{i}}}": form_data.get(f'd3_responsible{i}', ''),
                f"${{D3完成期限{i}}}": form_data.get(f'd3_deadline{i}', ''),
                f"${{D3状态{i}}}": form_data.get(f'd3_status{i}', ''),
                f"${{D3进度备注{i}}}": form_data.get(f'd3_note{i}', '')
            }
        
        # 添加D4人机料法环测原因分析 - 将同类别的原因整合成数组
        cause_categories = {
            'man': '人原因',
            'machine': '机原因', 
            'material': '料原因',
            'method': '法原因',
            'environment': '环原因',
            'measurement': '测原因'
        }
        
        for category, label in cause_categories.items():
            # 收集所有该类别的原因编号
            cause_numbers = set([1])  # 确保至少包含原因1
            for key in form_data.keys():
                if key.startswith(f'd4_{category}') and not key.endswith('_judgment') and not key.endswith('_evidence'):
                    suffix = key[len(f'd4_{category}'):]
                    if suffix.isdigit():
                        try:
                            cause_num = int(suffix)
                            cause_numbers.add(cause_num)
                        except ValueError:
                            continue
            
            # 构建该类别的所有原因数组
            causes_array = []
            for i in sorted(cause_numbers):
                # 提取类别名称（去掉"原因"）
                category_name = label.replace('原因', '')
                cause_item = {
                    f"${{D4{label}{i}}}": form_data.get(f'd4_{category}{i}', ''),
                    f"${{D4{category_name}判定{i}}}": form_data.get(f'd4_{category}{i}_judgment', ''),
                    f"${{D4{category_name}证据{i}}}": form_data.get(f'd4_{category}{i}_evidence', '')
                }
                causes_array.append(cause_item)
            
            # 将整个类别的原因作为一个整体赋值
            json_data["replacements"]["D4根本原因"]["人机料法环测分析"][label] = causes_array
        
        # 添加D5永久措施信息 - 确保至少包含措施1的空结构
        json_data["replacements"]["D5永久措施"]["措施1"] = {
            "${D5纠正措施1}": form_data.get('d5_measure1', ''),
            "${D5责任人1}": form_data.get('d5_responsible1', ''),
            "${D5计划完成日期1}": form_data.get('d5_deadline1', '')
        }
        
        # 动态添加额外的D5措施
        d5_measure_numbers = set()
        for key in form_data.keys():
            if key.startswith('d5_measure') and key[10:].isdigit():
                try:
                    measure_num = int(key[10:])
                    if measure_num > 1:  # 只处理措施2及以上
                        d5_measure_numbers.add(measure_num)
                except ValueError:
                    continue
        
        for i in sorted(d5_measure_numbers):
            json_data["replacements"]["D5永久措施"][f"措施{i}"] = {
                f"${{D5纠正措施{i}}}": form_data.get(f'd5_measure{i}', ''),
                f"${{D5责任人{i}}}": form_data.get(f'd5_responsible{i}', ''),
                f"${{D5计划完成日期{i}}}": form_data.get(f'd5_deadline{i}', '')
            }
        
        # 添加D6措施验证信息 - 确保至少包含验证1的空结构
        json_data["replacements"]["D6措施验证"]["验证1"] = {
            "${D6措施验证1}": form_data.get('d6_verification1', ''),
            "${D6验证人1}": form_data.get('d6_verifier1', ''),
            "${D6验证时间1}": form_data.get('d6_time1', ''),
            "${D6验证结果1}": form_data.get('d6_result1', '')
        }
        
        # 动态添加额外的D6验证
        d6_verification_numbers = set()
        for key in form_data.keys():
            if key.startswith('d6_verification') and key[15:].isdigit():
                try:
                    verification_num = int(key[15:])
                    if verification_num > 1:  # 只处理验证2及以上
                        d6_verification_numbers.add(verification_num)
                except ValueError:
                    continue
        
        for i in sorted(d6_verification_numbers):
            json_data["replacements"]["D6措施验证"][f"验证{i}"] = {
                f"${{D6措施验证{i}}}": form_data.get(f'd6_verification{i}', ''),
                f"${{D6验证人{i}}}": form_data.get(f'd6_verifier{i}', ''),
                f"${{D6验证时间{i}}}": form_data.get(f'd6_time{i}', ''),
                f"${{D6验证结果{i}}}": form_data.get(f'd6_result{i}', '')
            }
        
        # 添加D7预防措施信息 - 确保至少包含预防1的空结构
        json_data["replacements"]["D7预防措施"]["预防1"] = {
            "${D7预防措施1}": form_data.get('d7_prevention1', ''),
            "${D7责任人1}": form_data.get('d7_responsible1', ''),
            "${D7计划完成日期1}": form_data.get('d7_deadline1', '')
        }
        
        # 动态添加额外的D7预防措施
        d7_prevention_numbers = set()
        for key in form_data.keys():
            if key.startswith('d7_prevention') and key[13:].isdigit():
                try:
                    prevention_num = int(key[13:])
                    if prevention_num > 1:  # 只处理预防措施2及以上
                        d7_prevention_numbers.add(prevention_num)
                except ValueError:
                    continue
        
        for i in sorted(d7_prevention_numbers):
            json_data["replacements"]["D7预防措施"][f"预防{i}"] = {
                f"${{D7预防措施{i}}}": form_data.get(f'd7_prevention{i}', ''),
                f"${{D7责任人{i}}}": form_data.get(f'd7_responsible{i}', ''),
                f"${{D7计划完成日期{i}}}": form_data.get(f'd7_deadline{i}', '')
            }
        
        # 生成唯一的文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        random_suffix = str(uuid.uuid4())[:8]
        json_filename = f"8d_report_{timestamp}_{random_suffix}.json"
        json_filepath = os.path.join(D8_REPORTS_DIR, json_filename)
        
        # 保存JSON文件
        with open(json_filepath, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        # 使用flash消息并重定向到主页
        flash(f"8D报告数据已成功保存！文件名: {json_filename}", "success")
        return redirect(url_for('index'))
        
    except Exception as e:
        # 发生错误时也使用flash消息并重定向
        flash(f"保存失败: {str(e)}", "error")
        return redirect(url_for('index'))


# ==================== 新的协作编辑API端点 ====================

@app.route('/report_preview/<session_id>')
def report_preview(session_id):
    """报告预览编辑页面"""
    try:
        # 获取会话报告数据
        report_data = get_session_report(session_id)
        
        if not report_data:
            flash("会话已过期或不存在，请重新填写表单", "error")
            return redirect(url_for('index'))
        
        return render_template('report_preview.html', 
                               session_id=session_id,
                               report_data=report_data)
    except Exception as e:
        flash(f"加载预览页面失败: {str(e)}", "error")
        return redirect(url_for('index'))


@app.route('/api/get_report_preview/<session_id>', methods=['GET'])
def get_report_preview(session_id):
    """获取会话报告数据"""
    try:
        report_data = get_session_report(session_id)
        
        if not report_data:
            return jsonify({'error': '会话不存在或已过期'}), 404
        
        return jsonify({
            'status': 'success',
            'session_id': session_id,
            'report_data': report_data
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/refine_report', methods=['POST'])
def api_refine_report():
    """使用AI完善报告内容"""
    try:
        data = request.get_json()
        
        if not data or 'session_id' not in data or 'user_feedback' not in data:
            return jsonify({'error': '请求参数不完整'}), 400
        
        session_id = data['session_id']
        user_feedback = data['user_feedback']
        conversation_id = data.get('conversation_id')
        
        # 获取当前报告数据
        current_report = get_session_report(session_id)
        if not current_report:
            return jsonify({'error': '会话不存在或已过期'}), 404
        
        # 使用AI完善报告
        if DIFY_API_KEY and DIFY_API_KEY.startswith('app-'):
            result = refine_report_with_ai(current_report, user_feedback, conversation_id)
            
            # 打印AI对话后的数据变化
            if result['report_data'] != current_report:
                print("\n" + "=" * 80)
                print("💬 AI对话完善后的8D报告数据:")
                print(f"用户反馈: {user_feedback}")
                print("=" * 80)
                print(json.dumps(result['report_data'], ensure_ascii=False, indent=2))
                print("=" * 80 + "\n")
            
            # 保存更新后的报告
            save_session_report(session_id, result['report_data'])
            
            return jsonify({
                'status': 'success',
                'report_data': result['report_data'],
                'ai_response': result['ai_response'],
                'conversation_id': result['conversation_id']
            })
        else:
            return jsonify({
                'status': 'success',
                'report_data': current_report,
                'ai_response': '抱歉，AI服务未配置，无法自动完善报告内容。',
                'conversation_id': conversation_id
            })
            
    except Exception as e:
        print(f"完善报告时出错: {str(e)}")
        return jsonify({'error': f'处理失败: {str(e)}'}), 500


@app.route('/api/generate_final_documents', methods=['POST'])
def generate_final_documents():
    """生成最终的PPT和DOCX文档"""
    try:
        data = request.get_json()
        
        if not data or 'session_id' not in data:
            return jsonify({'error': '请求参数不完整'}), 400
        
        session_id = data['session_id']
        template_version = data.get('template_version', '0610')  # 默认使用0610版本
        
        # 获取报告数据
        report_data = get_session_report(session_id)
        if not report_data:
            return jsonify({'error': '会话不存在或已过期'}), 404
        
        # 将报告数据转换为标准的replacements格式
        # 这里需要根据实际的数据结构进行调整
        replacements_data = transform_report_to_replacements(report_data)
        
        # 生成PPT
        ppt_result = generate_ppt_document(template_version, replacements_data)
        
        # 生成DOCX  
        docx_result = generate_docx_document(template_version, replacements_data)
        
        # 保存最终的8D报告记录
        save_final_8d_report(session_id, report_data, ppt_result, docx_result)
        
        if ppt_result['status'] == 'success' and docx_result['status'] == 'success':
            return jsonify({
                'status': 'success',
                'message': '文档生成成功',
                'files': {
                    'ppt': ppt_result['download_url'],
                    'docx': docx_result['download_url']
                }
            })
        else:
            return jsonify({
                'status': 'partial_success',
                'message': '部分文档生成失败',
                'files': {
                    'ppt': ppt_result.get('download_url') if ppt_result['status'] == 'success' else None,
                    'docx': docx_result.get('download_url') if docx_result['status'] == 'success' else None
                },
                'errors': {
                    'ppt': ppt_result.get('error') if ppt_result['status'] != 'success' else None,
                    'docx': docx_result.get('error') if docx_result['status'] != 'success' else None
                }
            })
            
    except Exception as e:
        print(f"生成最终文档时出错: {str(e)}")
        return jsonify({'error': f'生成失败: {str(e)}'}), 500


def transform_report_to_replacements(report_data):
    """将AI完善后的报告数据转换为模板替换格式"""
    # 这里需要根据实际的数据结构进行调整
    # 假设report_data已经是合适的格式，直接返回
    # 实际使用时需要根据Dify返回的数据格式进行相应的转换
    return report_data


def generate_ppt_document(template_version, replacements_data):
    """生成PPT文档"""
    try:
        template_path = f"8d_template/{template_version}/template.pptx"
        
        if not os.path.exists(template_path):
            template_path = "template.pptx"  # 回退到根目录模板
        
        if not os.path.exists(template_path):
            return {'status': 'error', 'error': 'PPT模板文件不存在'}
        
        # 加载PPT模板
        prs = Presentation(template_path)
        
        # 扁平化替换数据
        all_replacements = flatten_json_data(replacements_data)
        
        # 处理整个PPT
        process_all_slides_in_ppt(prs, all_replacements)
        
        # 生成唯一的文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        random_suffix = str(uuid.uuid4())[:8]
        output_filename = f"{timestamp}_{random_suffix}.pptx"
        output_path = os.path.join(GENERATED_PPTS_DIR, output_filename)
        
        # 保存处理后的PPT
        prs.save(output_path)
        
        # 生成下载ID
        download_id = str(uuid.uuid4())
        FILE_MAPPING[download_id] = {
            'filepath': output_path,
            'filename': output_filename,
            'type': 'pptx',
            'created_at': datetime.now()
        }
        save_file_mapping()
        
        download_url = f"http://{request.host}/download_file/{download_id}"
        
        return {
            'status': 'success',
            'download_url': download_url,
            'filename': output_filename
        }
        
    except Exception as e:
        print(f"生成PPT文档时出错: {str(e)}")
        return {'status': 'error', 'error': str(e)}


def generate_docx_document(template_version, replacements_data):
    """生成DOCX文档"""
    try:
        template_path = f"8d_template/{template_version}/template.docx"
        
        if not os.path.exists(template_path):
            template_path = "template.docx"  # 回退到根目录模板
        
        if not os.path.exists(template_path):
            return {'status': 'error', 'error': 'DOCX模板文件不存在'}
        
        # 加载DOCX模板
        doc = Document(template_path)
        
        # 扁平化替换数据
        all_replacements = flatten_json_data(replacements_data)
        
        # 处理整个DOCX文档
        process_all_content_in_docx(doc, all_replacements)
        
        # 生成唯一的文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        random_suffix = str(uuid.uuid4())[:8]
        output_filename = f"{timestamp}_{random_suffix}.docx"
        output_path = os.path.join(GENERATED_DOCX_DIR, output_filename)
        
        # 保存处理后的DOCX
        doc.save(output_path)
        
        # 生成下载ID
        download_id = str(uuid.uuid4())
        FILE_MAPPING[download_id] = {
            'filepath': output_path,
            'filename': output_filename,
            'type': 'docx',
            'created_at': datetime.now()
        }
        save_file_mapping()
        
        download_url = f"http://{request.host}/download_file/{download_id}"
        
        return {
            'status': 'success',
            'download_url': download_url,
            'filename': output_filename
        }
        
    except Exception as e:
        print(f"生成DOCX文档时出错: {str(e)}")
        return {'status': 'error', 'error': str(e)}


def save_final_8d_report(session_id, report_data, ppt_result, docx_result):
    """保存最终的8D报告记录"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        random_suffix = str(uuid.uuid4())[:8]
        report_filename = f"8d_report_{timestamp}_{random_suffix}.json"
        report_filepath = os.path.join(D8_REPORTS_DIR, report_filename)
        
        final_report = {
            'session_id': session_id,
            'report_data': report_data,
            'generated_files': {
                'ppt': ppt_result if ppt_result['status'] == 'success' else None,
                'docx': docx_result if docx_result['status'] == 'success' else None
            },
            'created_at': datetime.now().isoformat(),
            'status': 'completed'
        }
        
        with open(report_filepath, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2)
        
        print(f"最终8D报告已保存: {report_filename}")
        
    except Exception as e:
        print(f"保存最终8D报告失败: {str(e)}")


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5555, debug=True) 