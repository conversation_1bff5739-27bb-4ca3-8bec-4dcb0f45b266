/**
 * 8D报告预览编辑页面 JavaScript
 */

// 全局状态管理
let currentReportData = null;
let conversationId = null;
let isProcessing = false;

// DOM元素引用
let chatHistory = null;
let chatInput = null;
let sendBtn = null;
let previewContent = null;
let generateBtn = null;
let loadingOverlay = null;

/**
 * 页面初始化
 */
function initializePage() {
    console.log('初始化8D报告预览页面');
    
    // 获取DOM元素引用
    chatHistory = document.getElementById('chat-history');
    chatInput = document.getElementById('chat-input');
    sendBtn = document.getElementById('send-btn');
    previewContent = document.getElementById('preview-content');
    generateBtn = document.getElementById('generate-documents-btn');
    loadingOverlay = document.getElementById('loading-overlay');
    
    // 初始化报告数据
    currentReportData = reportData;
    
    // 设置AI状态
    updateAIStatus('ready', '就绪');
    
    console.log('页面初始化完成');
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    console.log('绑定事件监听器');
    
    // 发送按钮点击
    if (sendBtn) {
        sendBtn.addEventListener('click', handleSendMessage);
    }
    
    // 输入框回车发送
    if (chatInput) {
        chatInput.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                handleSendMessage();
            }
        });
        
        // 字符计数
        chatInput.addEventListener('input', updateCharCount);
    }
    
    // 快速建议按钮
    document.querySelectorAll('.suggestion-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const suggestion = this.getAttribute('data-suggestion');
            if (chatInput) {
                chatInput.value = suggestion;
                updateCharCount();
                handleSendMessage();
            }
        });
    });
    
    // 语音输入按钮
    const voiceBtn = document.getElementById('voice-input-btn');
    if (voiceBtn) {
        voiceBtn.addEventListener('click', handleVoiceInput);
    }
    
    // 生成文档按钮
    if (generateBtn) {
        generateBtn.addEventListener('click', handleGenerateDocuments);
    }
    
    // 展开/收起按钮
    const expandBtn = document.getElementById('expand-all-btn');
    const collapseBtn = document.getElementById('collapse-all-btn');
    
    if (expandBtn) {
        expandBtn.addEventListener('click', () => expandAllSections());
    }
    
    if (collapseBtn) {
        collapseBtn.addEventListener('click', () => collapseAllSections());
    }
    
    // 保存会话按钮
    const saveBtn = document.getElementById('save-session-btn');
    if (saveBtn) {
        saveBtn.addEventListener('click', handleSaveSession);
    }
    
    console.log('事件监听器绑定完成');
}

/**
 * 渲染报告预览内容
 */
function renderReportPreview() {
    console.log('渲染报告预览内容');
    
    if (!previewContent) {
        console.error('预览内容容器未找到');
        return;
    }
    
    if (!currentReportData) {
        previewContent.innerHTML = '<div class="error-message">暂无报告数据</div>';
        return;
    }
    
    // 构建D0-D8的内容结构
    const sections = [
        {
            id: 'd0',
            title: 'D0 - 征兆紧急反应措施',
            items: [
                { label: '问题标题', key: 'd0_title' },
                { label: '汇报人', key: 'd0_reporter' },
                { label: '汇报时间', key: 'd0_time' },
                { label: '项目背景', key: 'd0_background' }
            ]
        },
        {
            id: 'd1',
            title: 'D1 - 成立改善小组',
            items: [
                { label: '组长姓名', key: 'd1_leader_name' },
                { label: '组长部门', key: 'd1_leader_dept' },
                { label: '组长职位', key: 'd1_leader_position' },
                { label: '主要职责', key: 'd1_leader_responsibility' }
            ]
        },
        {
            id: 'd2',
            title: 'D2 - 问题说明',
            items: [
                { label: '事件整体描述', key: 'd2_description' },
                { label: '何时发生', key: 'd2_when' },
                { label: '何地发生', key: 'd2_where' },
                { label: '何人发现', key: 'd2_who' },
                { label: '发生了什么问题', key: 'd2_what' },
                { label: '为什么是这问题', key: 'd2_why' },
                { label: '问题如何发生', key: 'd2_how' },
                { label: '问题影响程度', key: 'd2_impact' }
            ]
        },
        {
            id: 'd3',
            title: 'D3 - 实施并验证临时措施',
            items: [
                { label: '范围', key: 'd3_scope1' },
                { label: '处置对策', key: 'd3_measure1' },
                { label: '责任人', key: 'd3_responsible1' },
                { label: '完成期限', key: 'd3_deadline1' },
                { label: '状态', key: 'd3_status1' },
                { label: '进度备注', key: 'd3_note1' }
            ]
        },
        {
            id: 'd4',
            title: 'D4 - 确定并验证根本原因',
            items: [
                { label: '5Why分析1', key: 'd4_why1' },
                { label: '答案1', key: 'd4_answer1' },
                { label: '5Why分析2', key: 'd4_why2' },
                { label: '答案2', key: 'd4_answer2' },
                { label: '5Why分析3', key: 'd4_why3' },
                { label: '答案3', key: 'd4_answer3' },
                { label: '原因小结', key: 'd4_summary' }
            ]
        },
        {
            id: 'd5',
            title: 'D5 - 选择并验证永久纠正措施',
            items: [
                { label: '纠正措施', key: 'd5_measure1' },
                { label: '责任人', key: 'd5_responsible1' },
                { label: '计划完成日期', key: 'd5_deadline1' }
            ]
        },
        {
            id: 'd6',
            title: 'D6 - 实施永久纠正措施',
            items: [
                { label: '措施验证', key: 'd6_verification1' },
                { label: '验证人', key: 'd6_verifier1' },
                { label: '验证时间', key: 'd6_time1' },
                { label: '验证结果', key: 'd6_result1' }
            ]
        },
        {
            id: 'd7',
            title: 'D7 - 预防再发生',
            items: [
                { label: '预防措施', key: 'd7_prevention1' },
                { label: '责任人', key: 'd7_responsible1' },
                { label: '计划完成日期', key: 'd7_deadline1' }
            ]
        },
        {
            id: 'd8',
            title: 'D8 - 恭喜小组',
            items: [
                { label: '有效性确认', key: 'd8_effectiveness' },
                { label: '确认人', key: 'd8_confirmer' },
                { label: '确认完成时间', key: 'd8_confirm_time' }
            ]
        }
    ];
    
    let html = '';
    
    sections.forEach(section => {
        html += `
            <div class="d-section" id="section-${section.id}">
                <div class="d-section-header" onclick="toggleSection('${section.id}')">
                    <h3 class="d-section-title">${section.title}</h3>
                    <span class="d-section-toggle">▼</span>
                </div>
                <div class="d-section-content">
        `;
        
        section.items.forEach(item => {
            const value = currentReportData[item.key] || '';
            html += `
                <div class="content-item">
                    <div class="content-label">${item.label}</div>
                    <div class="content-value" data-key="${item.key}">${escapeHtml(value)}</div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
    });
    
    previewContent.innerHTML = html;
    console.log('报告预览内容渲染完成');
}

/**
 * 处理发送消息
 */
async function handleSendMessage() {
    if (isProcessing) return;
    
    const message = chatInput.value.trim();
    if (!message) return;
    
    console.log('发送消息:', message);
    
    // 添加用户消息到对话历史
    addMessageToHistory('user', message);
    
    // 清空输入框
    chatInput.value = '';
    updateCharCount();
    
    // 设置处理状态
    setProcessingState(true);
    
    try {
        // 调用AI完善报告
        const response = await refineReportWithAI(message);
        
        if (response.success) {
            // 更新报告数据
            currentReportData = response.data.report_data;
            conversationId = response.data.conversation_id;
            
            // 添加AI回复到对话历史
            addMessageToHistory('ai', response.data.ai_response);
            
            // 重新渲染预览内容
            renderReportPreview();
            
            // 高亮更新的内容
            highlightUpdatedContent();
            
        } else {
            addMessageToHistory('ai', '抱歉，处理您的请求时出现了问题：' + response.error);
        }
        
    } catch (error) {
        console.error('发送消息时出错:', error);
        addMessageToHistory('ai', '抱歉，服务暂时不可用，请稍后再试。');
    } finally {
        setProcessingState(false);
    }
}

/**
 * 使用AI完善报告
 */
async function refineReportWithAI(userFeedback) {
    try {
        const response = await fetch('/api/refine_report', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                session_id: SESSION_ID,
                user_feedback: userFeedback,
                conversation_id: conversationId
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            return {
                success: true,
                data: data
            };
        } else {
            const errorData = await response.json();
            return {
                success: false,
                error: errorData.error || '请求失败'
            };
        }
        
    } catch (error) {
        console.error('AI完善报告时出错:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * 添加消息到对话历史
 */
function addMessageToHistory(type, content) {
    if (!chatHistory) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}-message`;
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = escapeHtml(content).replace(/\n/g, '<br>');
    
    messageDiv.appendChild(contentDiv);
    chatHistory.appendChild(messageDiv);
    
    // 滚动到底部
    chatHistory.scrollTop = chatHistory.scrollHeight;
    
    // 添加动画效果
    messageDiv.style.opacity = '0';
    messageDiv.style.transform = 'translateY(20px)';
    setTimeout(() => {
        messageDiv.style.transition = 'all 0.3s ease';
        messageDiv.style.opacity = '1';
        messageDiv.style.transform = 'translateY(0)';
    }, 10);
}

/**
 * 处理语音输入
 */
function handleVoiceInput() {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        alert('您的浏览器不支持语音识别功能');
        return;
    }
    
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    
    recognition.lang = 'zh-CN';
    recognition.continuous = false;
    recognition.interimResults = false;
    
    const voiceBtn = document.getElementById('voice-input-btn');
    
    recognition.onstart = function() {
        voiceBtn.innerHTML = '<span class="icon">🔴</span>';
        voiceBtn.disabled = true;
        updateAIStatus('listening', '正在听取...');
    };
    
    recognition.onresult = function(event) {
        const result = event.results[0][0].transcript;
        if (chatInput) {
            chatInput.value = result;
            updateCharCount();
        }
    };
    
    recognition.onerror = function(event) {
        console.error('语音识别错误:', event.error);
        alert('语音识别失败，请重试');
    };
    
    recognition.onend = function() {
        voiceBtn.innerHTML = '<span class="icon">🎤</span>';
        voiceBtn.disabled = false;
        updateAIStatus('ready', '就绪');
    };
    
    recognition.start();
}

/**
 * 处理生成最终文档
 */
async function handleGenerateDocuments() {
    if (isProcessing) return;
    
    console.log('开始生成最终文档');
    
    // 显示生成状态
    const statusDiv = document.getElementById('generation-status');
    if (statusDiv) {
        statusDiv.style.display = 'block';
    }
    
    generateBtn.disabled = true;
    setProcessingState(true);
    
    try {
        const response = await fetch('/api/generate_final_documents', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                session_id: SESSION_ID,
                template_version: '0610'
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            
            if (data.status === 'success') {
                showSuccessModal(data.files);
            } else {
                alert('文档生成失败：' + data.message);
            }
        } else {
            const errorData = await response.json();
            alert('生成失败：' + (errorData.error || '未知错误'));
        }
        
    } catch (error) {
        console.error('生成文档时出错:', error);
        alert('生成失败：' + error.message);
    } finally {
        generateBtn.disabled = false;
        setProcessingState(false);
        
        if (statusDiv) {
            statusDiv.style.display = 'none';
        }
    }
}

/**
 * 显示成功模态框
 */
function showSuccessModal(files) {
    const modal = document.getElementById('success-modal');
    const linksContainer = document.getElementById('download-links');
    
    if (!modal || !linksContainer) return;
    
    let linksHtml = '';
    
    if (files.ppt) {
        linksHtml += `<a href="${files.ppt}" class="download-link" target="_blank">📊 下载PPT文档</a>`;
    }
    
    if (files.docx) {
        linksHtml += `<a href="${files.docx}" class="download-link" target="_blank">📄 下载DOCX文档</a>`;
    }
    
    linksContainer.innerHTML = linksHtml;
    modal.style.display = 'flex';
}

/**
 * 关闭模态框
 */
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * 切换章节展开/收起
 */
function toggleSection(sectionId) {
    const section = document.getElementById(`section-${sectionId}`);
    if (section) {
        section.classList.toggle('collapsed');
    }
}

/**
 * 展开所有章节
 */
function expandAllSections() {
    document.querySelectorAll('.d-section').forEach(section => {
        section.classList.remove('collapsed');
    });
}

/**
 * 收起所有章节
 */
function collapseAllSections() {
    document.querySelectorAll('.d-section').forEach(section => {
        section.classList.add('collapsed');
    });
}

/**
 * 高亮更新的内容
 */
function highlightUpdatedContent() {
    document.querySelectorAll('.d-section').forEach(section => {
        section.classList.add('updated');
        setTimeout(() => {
            section.classList.remove('updated');
        }, 2000);
    });
}

/**
 * 更新字符计数
 */
function updateCharCount() {
    if (!chatInput) return;
    
    const charCountSpan = document.getElementById('char-count');
    if (charCountSpan) {
        charCountSpan.textContent = chatInput.value.length;
    }
}

/**
 * 设置处理状态
 */
function setProcessingState(processing) {
    isProcessing = processing;
    
    if (processing) {
        updateAIStatus('processing', '处理中...');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }
        if (sendBtn) {
            sendBtn.disabled = true;
        }
    } else {
        updateAIStatus('ready', '就绪');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
        if (sendBtn) {
            sendBtn.disabled = false;
        }
    }
}

/**
 * 更新AI状态显示
 */
function updateAIStatus(status, text) {
    const statusIndicator = document.getElementById('ai-status');
    const statusText = document.getElementById('ai-status-text');
    
    if (statusIndicator) {
        switch (status) {
            case 'ready':
                statusIndicator.style.color = '#4ade80'; // 绿色
                break;
            case 'processing':
                statusIndicator.style.color = '#f59e0b'; // 黄色
                break;
            case 'listening':
                statusIndicator.style.color = '#ef4444'; // 红色
                break;
            case 'error':
                statusIndicator.style.color = '#ef4444'; // 红色
                break;
            default:
                statusIndicator.style.color = '#6b7280'; // 灰色
        }
    }
    
    if (statusText) {
        statusText.textContent = text;
    }
}

/**
 * 处理保存会话
 */
async function handleSaveSession() {
    try {
        // 这里可以实现保存会话的逻辑
        // 比如将当前对话历史保存到服务器
        alert('会话已保存');
    } catch (error) {
        console.error('保存会话时出错:', error);
        alert('保存失败：' + error.message);
    }
}

/**
 * HTML转义函数
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * 页面卸载前的清理工作
 */
window.addEventListener('beforeunload', function(e) {
    // 如果有未完成的处理，提醒用户
    if (isProcessing) {
        e.preventDefault();
        e.returnValue = '正在处理中，确定要离开吗？';
    }
});

// 全局函数导出（用于HTML中的onclick调用）
window.toggleSection = toggleSection;
window.closeModal = closeModal;

console.log('报告预览页面JavaScript加载完成'); 