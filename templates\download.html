<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件下载</title>
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/download.css') }}">
    <script src="{{ url_for('static', filename='js/download.js') }}"></script>
</head>
<body>
    <div class="download-container" data-download-id="{{ download_id }}" data-filename="{{ filename }}">
        <div class="download-icon">📄</div>
        <h1 class="download-title">文件准备就绪</h1>
        <p class="download-subtitle">您的文档已生成完成，可以下载了</p>
        
        {% if error %}
        <div class="error-message">
            ⚠️ {{ error }}
        </div>
        {% else %}
        <div class="file-info">
            <div class="file-name">{{ filename }}</div>
            <div class="file-type">{{ file_type }} 文档</div>
        </div>
        
        <button class="download-btn" onclick="startDownload()">
            🔽 立即下载
        </button>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">准备下载...</div>
        </div>
        {% endif %}
    </div>
</body>
</html> 